# 声音库视频翻译流程技术设计方案

## 项目概述

**项目名称**: 声音库视频翻译功能开发  
**设计模式**: 单任务流水线处理方案  
**基础架构**: 统一任务系统 (UniversalTaskProcessor)  
**开发日期**: 2025-08-04  
**版本**: v1.0

## 需求分析

### 功能需求
1. **视频上传**: 用户上传视频文件到OSS
2. **字幕提取**: 使用阿里云视频智能服务提取视频字幕
3. **字幕翻译**: 将提取的字幕翻译成目标语言
4. **语音合成**: 使用Minimax或直接翻译成目标语言语音
5. **视频合成**: 将新语音与原视频合成生成最终结果

### 非功能需求
- **可靠性**: 支持任务失败重试和故障恢复
- **可监控性**: 详细的进度跟踪和日志记录
- **可扩展性**: 支持多种服务商和处理策略
- **一致性**: 遵循现有统一任务系统架构

## 技术架构设计

### 核心组件架构

```
用户请求 → VideoTranslateController → UniversalTaskService
    ↓
UniversalVoiceLibraryProcessor (新增)
    ↓
流水线处理步骤:
1. 视频上传OSS
2. 阿里云字幕提取
3. 字幕翻译处理
4. 语音合成(Minimax)
5. 视频音频合成
    ↓
任务完成 → 回调处理 → 状态更新
```

### 关键设计原则

1. **SOLID原则遵循**:
   - **SRP**: 每个处理器专注单一职责
   - **OCP**: 通过接口扩展支持新服务商
   - **DIP**: 依赖抽象而非具体实现

2. **KISS & YAGNI**:
   - 简化流程设计，避免过度抽象
   - 只实现当前明确需求的功能

3. **DRY**:
   - 复用现有统一任务系统组件
   - 抽象通用的服务调用逻辑

## 详细实现方案

### 1. 新增任务类型定义

**位置**: `com.nacos.entity.enums.TaskType`

```java
// 新增枚举值
VIDEO_TRANSLATE_VOICE_LIBRARY(4, "视频翻译-声音库", "使用声音库进行视频翻译")
```

### 2. 扩展请求参数模型

**位置**: `com.nacos.entity.dto.VideoTranslateRequestDTO`

```java
// 新增字段
/**
 * 处理模式: ORIGINAL_VOICE(原声翻译) / VOICE_LIBRARY(声音库)
 */
private String processMode = "ORIGINAL_VOICE";

/**
 * 声音库相关参数
 */
private VoiceLibraryParams voiceLibraryParams;

@Data
public static class VoiceLibraryParams {
    /**
     * 选择的声音库ID
     */
    private String voiceLibraryId;
    
    /**
     * 字幕提取服务商 (默认阿里云)
     */
    private String subtitleProvider = "ALIYUN";
    
    /**
     * 语音合成服务商 (默认Minimax)
     */
    private String voiceSynthesisProvider = "MINIMAX";
    
    /**
     * 视频合成服务商 (默认FFmpeg)
     */
    private String videoCompositionProvider = "FFMPEG";
}
```

### 3. 核心处理器实现

**位置**: `com.nacos.service.processor.impl.UniversalVideoTranslateVoiceLibraryProcessor`

#### 3.1 处理器类结构

```java
@Component("videoTranslateVoiceLibraryProcessor")
@Slf4j
public class UniversalVideoTranslateVoiceLibraryProcessor implements UniversalTaskProcessor<VideoTranslateRequestDTO> {
    
    // 依赖注入的服务组件
    @Autowired private AliyunSubtitleService aliyunSubtitleService;
    @Autowired private MinimaxVoiceService minimaxVoiceService;
    @Autowired private VideoCompositionService videoCompositionService;
    @Autowired private VideoTranslateTaskMapper videoTranslateTaskMapper;
    
    // 处理步骤枚举 - 详细的视频翻译声音库处理流程
    private enum ProcessStep {
        INIT_TASK("初始化任务", 5),
        VALIDATE_PARAMS("参数验证", 8),
        UPLOAD_VIDEO("视频上传OSS", 12),
        EXTRACT_SUBTITLE("阿里云字幕提取", 25),
        PARSE_SUBTITLE("解析字幕格式", 30),
        TRANSLATE_SUBTITLE("字幕文本翻译", 45),
        SYNTHESIZE_VOICE("Minimax语音合成", 65),
        MERGE_AUDIO("合并音频片段", 75),
        COMPOSE_VIDEO("FFmpeg视频合成", 90),
        UPLOAD_RESULT("上传最终结果", 95),
        COMPLETED("任务完成", 100);
        
        private final String description;
        private final int progress;
        
        ProcessStep(String description, int progress) {
            this.description = description;
            this.progress = progress;
        }
        
        public String getDescription() { return description; }
        public int getProgress() { return progress; }
    }
}
```

#### 3.2 主处理流程

```java
@Override
public TaskProcessResult process(TaskContext<VideoTranslateRequestDTO> context) {
    String methodName = "process";
    String taskId = context.getTaskId();
    String userId = context.getUserId();
    VideoTranslateRequestDTO request = context.getBusinessParams();
    
    try {
        log.info("[{}] 开始处理视频翻译声音库任务: taskId={}, userId={}", methodName, taskId, userId);
        
        // 步骤1: 初始化任务
        updateProgress(taskId, ProcessStep.INIT_TASK);
        initializeTask(taskId, request);
        
        // 步骤2: 参数验证
        updateProgress(taskId, ProcessStep.VALIDATE_PARAMS);
        if (!validateVoiceLibraryRequest(request)) {
            return TaskProcessResult.failure("声音库参数验证失败");
        }
        
        // 步骤3: 获取任务记录
        VideoTranslateTaskPO taskPO = getTaskRecord(taskId);
        if (taskPO == null) {
            return TaskProcessResult.failure("任务记录不存在");
        }
        
        // 步骤4: 执行详细的视频翻译流水线处理
        return executeDetailedProcessingPipeline(taskPO, request);
        
    } catch (Exception e) {
        log.error("[{}] 视频翻译声音库处理异常: taskId={}", methodName, taskId, e);
        updateTaskError(taskId, ProcessStep.INIT_TASK, "处理异常: " + e.getMessage());
        return TaskProcessResult.retryableFailure("处理异常: " + e.getMessage(), 300);
    }
}

/**
 * 执行详细的视频翻译声音库处理流水线
 */
private TaskProcessResult executeDetailedProcessingPipeline(VideoTranslateTaskPO taskPO, VideoTranslateRequestDTO request) {
    String taskId = taskPO.getTaskId();
    String methodName = "executeDetailedProcessingPipeline";
    
    try {
        log.info("[{}] 开始执行视频翻译声音库流水线: taskId={}", methodName, taskId);
        
        // 步骤3: 确认视频上传状态 (Controller层已完成，这里验证)
        updateProgress(taskId, ProcessStep.UPLOAD_VIDEO);
        validateVideoUpload(taskPO.getSourceVideoUrl());
        
        // 步骤4: 阿里云字幕提取
        updateProgress(taskId, ProcessStep.EXTRACT_SUBTITLE);
        SubtitleExtractionResult subtitleResult = extractVideoSubtitle(taskPO, request);
        logProcessingTrace(taskId, ProcessStep.EXTRACT_SUBTITLE, "字幕提取完成", subtitleResult);
        
        // 步骤5: 解析字幕格式
        updateProgress(taskId, ProcessStep.PARSE_SUBTITLE);
        List<SubtitleSegment> parsedSegments = parseSubtitleContent(subtitleResult);
        logProcessingTrace(taskId, ProcessStep.PARSE_SUBTITLE, "字幕解析完成", 
            Map.of("segmentCount", parsedSegments.size()));
        
        // 步骤6: 字幕文本翻译
        updateProgress(taskId, ProcessStep.TRANSLATE_SUBTITLE);
        SubtitleTranslationResult translationResult = translateSubtitleSegments(parsedSegments, request);
        logProcessingTrace(taskId, ProcessStep.TRANSLATE_SUBTITLE, "字幕翻译完成", translationResult);
        
        // 步骤7: Minimax语音合成
        updateProgress(taskId, ProcessStep.SYNTHESIZE_VOICE);
        VoiceSynthesisResult voiceResult = synthesizeVoiceSegments(translationResult, request);
        logProcessingTrace(taskId, ProcessStep.SYNTHESIZE_VOICE, "语音合成完成", 
            Map.of("audioSegmentCount", voiceResult.getAudioSegments().size()));
        
        // 步骤8: 合并音频片段
        updateProgress(taskId, ProcessStep.MERGE_AUDIO);
        String mergedAudioUrl = mergeAudioSegments(voiceResult);
        logProcessingTrace(taskId, ProcessStep.MERGE_AUDIO, "音频合并完成", 
            Map.of("mergedAudioUrl", mergedAudioUrl));
        
        // 步骤9: FFmpeg视频合成
        updateProgress(taskId, ProcessStep.COMPOSE_VIDEO);
        VideoCompositionResult compositionResult = composeVideoWithNewAudio(taskPO, mergedAudioUrl, request);
        logProcessingTrace(taskId, ProcessStep.COMPOSE_VIDEO, "视频合成完成", compositionResult);
        
        // 步骤10: 上传最终结果
        updateProgress(taskId, ProcessStep.UPLOAD_RESULT);
        String finalVideoUrl = uploadFinalVideo(compositionResult);
        logProcessingTrace(taskId, ProcessStep.UPLOAD_RESULT, "结果上传完成", 
            Map.of("finalVideoUrl", finalVideoUrl));
        
        // 步骤11: 完成任务
        updateProgress(taskId, ProcessStep.COMPLETED);
        VideoTranslateVoiceLibraryResult finalResult = buildFinalResult(compositionResult, finalVideoUrl);
        completeVideoTranslateTask(taskId, finalResult);
        
        log.info("[{}] 视频翻译声音库处理完成: taskId={}, finalVideoUrl={}", 
                methodName, taskId, finalVideoUrl);
        
        return TaskProcessResult.success("视频翻译声音库处理完成", finalResult);
        
    } catch (ProcessingException e) {
        log.error("[{}] 视频翻译声音库流水线处理失败: taskId={}, step={}", methodName, taskId, e.getFailedStep(), e);
        updateTaskError(taskId, e.getFailedStep(), e.getMessage());
        return TaskProcessResult.retryableFailure(e.getMessage(), e.getRetryDelaySeconds());
    } catch (Exception e) {
        log.error("[{}] 视频翻译声音库流水线处理异常: taskId={}", methodName, taskId, e);
        updateTaskError(taskId, ProcessStep.COMPOSE_VIDEO, "流水线处理异常: " + e.getMessage());
        return TaskProcessResult.retryableFailure("流水线处理异常: " + e.getMessage(), 300);
    }
}

/**
 * 初始化视频翻译声音库任务
 */
private void initializeTask(String taskId, VideoTranslateRequestDTO request) {
    String methodName = "initializeTask";
    try {
        log.info("[{}] 初始化视频翻译声音库任务: taskId={}", methodName, taskId);
        
        // 记录任务开始时间和参数
        Map<String, Object> initData = new HashMap<>();
        initData.put("processMode", request.getProcessMode());
        initData.put("voiceLibraryParams", request.getVoiceLibraryParams());
        initData.put("sourceLanguage", request.getSourceLanguage());
        initData.put("targetLanguage", request.getTargetLanguage());
        initData.put("startTime", System.currentTimeMillis());
        
        logProcessingTrace(taskId, ProcessStep.INIT_TASK, "任务初始化", initData);
        
    } catch (Exception e) {
        log.error("[{}] 初始化任务失败: taskId={}", methodName, taskId, e);
        throw new ProcessingException(ProcessStep.INIT_TASK, "初始化任务失败: " + e.getMessage(), 60);
    }
}

/**
 * 验证视频上传状态
 */
private void validateVideoUpload(String videoUrl) {
    String methodName = "validateVideoUpload";
    try {
        if (StringUtils.isEmpty(videoUrl)) {
            throw new ProcessingException(ProcessStep.UPLOAD_VIDEO, "视频URL为空", 0);
        }
        
        // TODO: 可以添加视频文件存在性验证
        log.info("[{}] 视频上传验证通过: videoUrl={}", methodName, videoUrl);
        
    } catch (ProcessingException e) {
        throw e;
    } catch (Exception e) {
        log.error("[{}] 视频上传验证失败: videoUrl={}", methodName, videoUrl, e);
        throw new ProcessingException(ProcessStep.UPLOAD_VIDEO, "视频上传验证失败: " + e.getMessage(), 60);
    }
}
```

### 4. 服务集成实现

#### 4.1 阿里云字幕提取服务

**位置**: `com.nacos.service.integration.AliyunSubtitleService`

```java
@Service
@Slf4j
public class AliyunSubtitleService {
    
    @Autowired
    private AliyunConfig aliyunConfig;
    
    /**
     * 提取视频字幕 - 用于视频翻译声音库功能
     */
    public SubtitleExtractionResult extractVideoSubtitle(String videoUrl, String sourceLanguage) {
        String methodName = "extractSubtitle";
        try {
            log.info("[{}] 开始阿里云视频字幕提取: videoUrl={}, language={}", methodName, videoUrl, sourceLanguage);
            
            // 调用阿里云视频智能API
            AliyunSubtitleRequest request = AliyunSubtitleRequest.builder()
                .videoUrl(videoUrl)
                .language(sourceLanguage)
                .outputFormat("SRT")
                .build();
            
            AliyunSubtitleResponse response = callAliyunSubtitleAPI(request);
            
            if (!response.isSuccess()) {
                throw new ProcessingException(ProcessStep.EXTRACT_SUBTITLE, "阿里云字幕提取失败: " + response.getMessage(), 300);
            }
            
            return SubtitleExtractionResult.builder()
                .subtitleContent(response.getSubtitleContent())
                .subtitleFormat("SRT")
                .extractionTime(System.currentTimeMillis())
                .build();
                
        } catch (Exception e) {
            log.error("[{}] 阿里云视频字幕提取异常: videoUrl={}", methodName, videoUrl, e);
            throw new ProcessingException(ProcessStep.EXTRACT_SUBTITLE, "阿里云字幕提取失败: " + e.getMessage(), 300);
        }
    }
}
```

#### 4.2 字幕翻译服务

**位置**: `com.nacos.service.integration.SubtitleTranslationService`

```java
@Service
@Slf4j  
public class SubtitleTranslationService {
    
    @Autowired
    private MinimaxTranslationService minimaxTranslationService;
    
    /**
     * 翻译字幕内容 - 视频翻译声音库专用
     */
    public SubtitleTranslationResult translateVideoSubtitle(SubtitleExtractionResult extractionResult, 
                                                     String sourceLanguage, String targetLanguage) {
        String methodName = "translateSubtitle";
        try {
            log.info("[{}] 开始视频字幕翻译: sourceLanguage={}, targetLanguage={}", 
                    methodName, sourceLanguage, targetLanguage);
            
            // 解析SRT字幕格式
            List<SubtitleSegment> originalSegments = parseSRTContent(extractionResult.getSubtitleContent());
            
            // 批量翻译字幕段落
            List<SubtitleSegment> translatedSegments = new ArrayList<>();
            for (SubtitleSegment segment : originalSegments) {
                String translatedText = minimaxTranslationService.translate(
                    segment.getText(), sourceLanguage, targetLanguage);
                
                translatedSegments.add(SubtitleSegment.builder()
                    .index(segment.getIndex())
                    .startTime(segment.getStartTime())
                    .endTime(segment.getEndTime())
                    .text(translatedText)
                    .build());
            }
            
            // 生成翻译后的SRT内容
            String translatedSRTContent = generateSRTContent(translatedSegments);
            
            return SubtitleTranslationResult.builder()
                .originalSegments(originalSegments)
                .translatedSegments(translatedSegments)
                .translatedSRTContent(translatedSRTContent)
                .translationTime(System.currentTimeMillis())
                .build();
                
        } catch (Exception e) {
            log.error("[{}] 视频字幕翻译异常", methodName, e);
            throw new ProcessingException(ProcessStep.TRANSLATE_SUBTITLE, "视频字幕翻译失败: " + e.getMessage(), 300);
        }
    }
}
```

#### 4.3 Minimax语音合成服务

**位置**: `com.nacos.service.integration.MinimaxVoiceService`

```java
@Service
@Slf4j
public class MinimaxVoiceService {
    
    @Autowired
    private MinimaxConfig minimaxConfig;
    
    /**
     * 合成语音文件 - 视频翻译声音库专用
     */
    public VoiceSynthesisResult synthesizeVideoVoice(SubtitleTranslationResult translationResult, 
                                              String voiceId, String targetLanguage) {
        String methodName = "synthesizeVoice";
        try {
            log.info("[{}] 开始Minimax语音合成: voiceId={}, language={}", methodName, voiceId, targetLanguage);
            
            List<AudioSegment> audioSegments = new ArrayList<>();
            
            // 为每个字幕段落合成音频
            for (SubtitleSegment segment : translationResult.getTranslatedSegments()) {
                MinimaxVoiceRequest request = MinimaxVoiceRequest.builder()
                    .text(segment.getText())
                    .voiceId(voiceId)
                    .language(targetLanguage)
                    .format("MP3")
                    .build();
                
                MinimaxVoiceResponse response = callMinimaxVoiceAPI(request);
                
                if (!response.isSuccess()) {
                    throw new ProcessingException(ProcessStep.SYNTHESIZE_VOICE, 
                        "Minimax语音合成失败: " + response.getMessage(), 300);
                }
                
                // 上传音频片段到OSS
                String audioUrl = uploadAudioToOSS(response.getAudioData(), segment.getIndex());
                
                audioSegments.add(AudioSegment.builder()
                    .index(segment.getIndex())
                    .startTime(segment.getStartTime())
                    .endTime(segment.getEndTime())
                    .audioUrl(audioUrl)
                    .duration(response.getDuration())
                    .build());
            }
            
            return VoiceSynthesisResult.builder()
                .audioSegments(audioSegments)
                .totalDuration(calculateTotalDuration(audioSegments))
                .synthesisTime(System.currentTimeMillis())
                .build();
                
        } catch (Exception e) {
            log.error("[{}] Minimax语音合成异常", methodName, e);
            throw new ProcessingException(ProcessStep.SYNTHESIZE_VOICE, "Minimax语音合成失败: " + e.getMessage(), 300);
        }
    }
}
```

#### 4.4 视频合成服务

**位置**: `com.nacos.service.integration.VideoCompositionService`

```java
@Service
@Slf4j
public class VideoCompositionService {
    
    @Autowired
    private FFmpegService ffmpegService;
    
    /**
     * 合成最终视频 - 视频翻译声音库专用
     */
    public VideoCompositionResult composeVideoWithAudio(String originalVideoUrl, 
                                             VoiceSynthesisResult voiceResult) {
        String methodName = "composeVideo";
        try {
            log.info("[{}] 开始FFmpeg视频合成: originalVideoUrl={}", methodName, originalVideoUrl);
            
            // 1. 合并所有音频片段为完整音轨
            String mergedAudioUrl = mergeAudioSegments(voiceResult.getAudioSegments());
            
            // 2. 使用FFmpeg替换原视频音轨
            FFmpegCompositionRequest request = FFmpegCompositionRequest.builder()
                .originalVideoUrl(originalVideoUrl)
                .newAudioUrl(mergedAudioUrl)
                .outputFormat("MP4")
                .quality("HIGH")
                .build();
            
            FFmpegCompositionResponse response = ffmpegService.composeVideo(request);
            
            if (!response.isSuccess()) {
                throw new ProcessingException(ProcessStep.COMPOSE_VIDEO, 
                    "视频合成失败: " + response.getMessage(), 300);
            }
            
            // 3. 上传最终视频到OSS
            String finalVideoUrl = uploadVideoToOSS(response.getVideoData());
            
            return VideoCompositionResult.builder()
                .finalVideoUrl(finalVideoUrl)
                .originalVideoUrl(originalVideoUrl)
                .audioUrl(mergedAudioUrl)
                .duration(response.getDuration())
                .fileSize(response.getFileSize())
                .compositionTime(System.currentTimeMillis())
                .build();
                
        } catch (Exception e) {
            log.error("[{}] FFmpeg视频合成异常", methodName, e);
            throw new ProcessingException(ProcessStep.COMPOSE_VIDEO, "FFmpeg视频合成失败: " + e.getMessage(), 300);
        }
    }
}
```

### 5. 数据模型定义

#### 5.1 处理结果模型

```java
// 字幕提取结果
@Data
@Builder
public class SubtitleExtractionResult {
    private String subtitleContent;    // SRT格式字幕内容
    private String subtitleFormat;     // 字幕格式 (SRT/VTT)
    private Long extractionTime;       // 提取时间戳
}

// 字幕翻译结果  
@Data
@Builder
public class SubtitleTranslationResult {
    private List<SubtitleSegment> originalSegments;    // 原始字幕段落
    private List<SubtitleSegment> translatedSegments;  // 翻译后字幕段落
    private String translatedSRTContent;               // 翻译后SRT内容
    private Long translationTime;                      // 翻译时间戳
}

// 语音合成结果
@Data
@Builder
public class VoiceSynthesisResult {
    private List<AudioSegment> audioSegments;  // 音频段落列表
    private Long totalDuration;                // 总时长(毫秒)
    private Long synthesisTime;                // 合成时间戳
}

// 视频合成结果
@Data
@Builder
public class VideoCompositionResult {
    private String finalVideoUrl;      // 最终视频URL
    private String originalVideoUrl;   // 原始视频URL
    private String audioUrl;          // 音频URL
    private Long duration;            // 视频时长(毫秒)
    private Long fileSize;            // 文件大小(字节)
    private Long compositionTime;     // 合成时间戳
}

// 视频翻译声音库最终结果
@Data
@Builder
public class VideoTranslateVoiceLibraryResult {
    private String taskId;                    // 任务ID
    private String finalVideoUrl;             // 最终翻译后视频URL
    private String originalVideoUrl;          // 原始视频URL
    private String mergedAudioUrl;            // 合并后音频URL 
    private String subtitleContent;           // 提取的字幕内容
    private String translatedSubtitleContent; // 翻译后字幕内容
    private Integer totalSegments;            // 字幕段落总数
    private Long totalDuration;               // 总时长(毫秒)
    private Long fileSize;                    // 最终文件大小(字节)
    private Long processingTime;              // 总处理耗时(毫秒)
    private String sourceLanguage;            // 源语言
    private String targetLanguage;            // 目标语言
    private String voiceId;                   // 使用的声音ID
    private Long completedTime;               // 完成时间戳
}
```

#### 5.2 字幕段落模型

```java
@Data
@Builder
public class SubtitleSegment {
    private Integer index;        // 段落索引
    private String startTime;     // 开始时间 (SRT格式: 00:00:01,000)
    private String endTime;       // 结束时间
    private String text;          // 字幕文本
}

@Data
@Builder  
public class AudioSegment {
    private Integer index;        // 段落索引
    private String startTime;     // 对应字幕开始时间
    private String endTime;       // 对应字幕结束时间
    private String audioUrl;      // 音频片段URL
    private Long duration;        // 音频时长(毫秒)
}
```

### 6. 异常处理设计

#### 6.1 处理异常类

```java
public class ProcessingException extends RuntimeException {
    private final ProcessStep failedStep;
    private final int retryDelaySeconds;
    
    public ProcessingException(ProcessStep failedStep, String message, int retryDelaySeconds) {
        super(message);
        this.failedStep = failedStep;
        this.retryDelaySeconds = retryDelaySeconds;
    }
    
    // Getters...
}
```

#### 6.2 错误恢复策略

```java
// 在处理器中实现重试逻辑
private TaskProcessResult executeWithRetry(Supplier<TaskProcessResult> operation, ProcessStep step) {
    int maxRetries = 3;
    int retryCount = 0;
    
    while (retryCount < maxRetries) {
        try {
            return operation.get();
        } catch (ProcessingException e) {
            retryCount++;
            if (retryCount >= maxRetries) {
                log.error("步骤{}执行失败，已达最大重试次数: {}", step, e.getMessage());
                return TaskProcessResult.failure("步骤执行失败: " + e.getMessage());
            }
            
            log.warn("步骤{}执行失败，进行第{}次重试: {}", step, retryCount, e.getMessage());
            try {
                Thread.sleep(e.getRetryDelaySeconds() * 1000L);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                return TaskProcessResult.failure("任务被中断");
            }
        }
    }
    
    return TaskProcessResult.failure("重试逻辑异常");
}
```

### 7. 配置管理

#### 7.1 声音库配置扩展

**位置**: `application-{profile}.yml`

```yaml
universal-task:
  video-translate:
    voice-library:
      # 声音库处理开关
      enabled: true
      
      # 阿里云字幕提取配置
      aliyun-subtitle:
        endpoint: "https://videoenhan.cn-shanghai.aliyuncs.com"
        access-key-id: "${ALIYUN_ACCESS_KEY_ID}"
        access-key-secret: "${ALIYUN_ACCESS_KEY_SECRET}"
        timeout: 300000  # 5分钟
        
      # Minimax语音合成配置  
      minimax-voice:
        api-url: "https://api.minimax.chat/v1/text_to_speech"
        api-key: "${MINIMAX_API_KEY}"
        timeout: 180000  # 3分钟
        
      # FFmpeg视频合成配置
      ffmpeg:
        enabled: true
        temp-dir: "/tmp/video_compose"
        quality: "HIGH"
        timeout: 600000  # 10分钟
```

#### 7.2 配置类扩展

```java
// 扩展 UniversalTaskConfig
@Data
public static class VoiceLibraryConfig {
    private Boolean enabled = true;
    
    @Valid
    private AliyunSubtitleConfig aliyunSubtitle = new AliyunSubtitleConfig();
    
    @Valid 
    private MinimaxVoiceConfig minimaxVoice = new MinimaxVoiceConfig();
    
    @Valid
    private FFmpegConfig ffmpeg = new FFmpegConfig();
}
```

### 8. 监控和日志

#### 8.1 详细进度跟踪

```java
private void updateProgress(String taskId, ProcessStep step) {
    try {
        Map<String, Object> progressData = new HashMap<>();
        progressData.put("currentStep", step.name());
        progressData.put("stepDescription", step.getDescription());
        progressData.put("progress", step.getProgress());
        progressData.put("updateTime", System.currentTimeMillis());
        
        // 更新任务记录中的进度信息
        VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.getTaskByTaskId(taskId);
        if (taskPO != null) {
            taskPO.setResultJson(objectMapper.writeValueAsString(progressData));
            taskPO.setUpdateTime(LocalDateTime.now());
            videoTranslateTaskMapper.updateById(taskPO);
        }
        
        log.info("声音库视频翻译进度更新: taskId={}, step={}, progress={}%", 
                taskId, step.getDescription(), step.getProgress());
                
    } catch (Exception e) {
        log.error("更新任务进度失败: taskId={}, step={}", taskId, step, e);
    }
}
```

#### 8.2 链路追踪日志

```java
// 在每个处理步骤中添加链路追踪
private void logProcessingTrace(String taskId, ProcessStep step, String operation, Object data) {
    Map<String, Object> traceLog = new HashMap<>();
    traceLog.put("taskId", taskId);
    traceLog.put("step", step.name());
    traceLog.put("operation", operation);
    traceLog.put("timestamp", System.currentTimeMillis());
    traceLog.put("data", data);
    
    log.info("声音库处理链路追踪: {}", objectMapper.writeValueAsString(traceLog));
}
```

## 集成方案

### 1. 控制器层扩展

**位置**: `VideoTranslateController.submitTranslateTask`

```java
// 在现有方法中添加处理模式判断
if ("VOICE_LIBRARY".equals(request.getProcessMode())) {
    // 使用声音库任务类型
    TaskMessage taskMessage = TaskMessage.userSubmit(taskId, request.getUserId(), TaskType.VIDEO_TRANSLATE_VOICE_LIBRARY);
    Result<Map<String, Object>> submitResult = universalTaskService.submitTask(taskMessage);
    // ... 处理结果
} else {
    // 原有的原声翻译逻辑
    TaskMessage taskMessage = TaskMessage.userSubmit(taskId, request.getUserId(), TaskType.VIDEO_TRANSLATE);
    // ... 现有逻辑
}
```

### 2. 处理器工厂扩展

**位置**: `UniversalTaskProcessorFactory`

```java
// 注册新的视频翻译声音库处理器
@PostConstruct
public void registerProcessors() {
    // 现有处理器注册...
    
    // 注册视频翻译声音库处理器
    registerProcessor(TaskType.VIDEO_TRANSLATE_VOICE_LIBRARY, videoTranslateVoiceLibraryProcessor);
}
```

### 3. 数据库扩展

**位置**: `VideoTranslateTaskPO`

```java
// 添加处理模式字段
/**
 * 处理模式: ORIGINAL_VOICE / VOICE_LIBRARY
 */
@TableField("process_mode")
private String processMode;

/**
 * 声音库参数JSON
 */
@TableField("voice_library_params")
private String voiceLibraryParams;
```

## 部署和测试策略

### 1. 分阶段部署

**Phase 1: 基础框架**
- 实现处理器基础结构
- 集成阿里云字幕提取服务
- 单元测试覆盖

**Phase 2: 核心功能**  
- 实现字幕翻译和语音合成
- 集成测试和功能验证
- 性能测试和优化

**Phase 3: 完整集成**
- 视频合成功能实现
- 端到端测试
- 生产环境部署

### 2. 测试用例设计

```java
@SpringBootTest
class UniversalVoiceLibraryProcessorTest {
    
    @Test
    void testCompleteVoiceLibraryProcess() {
        // 测试完整的声音库处理流程
    }
    
    @Test  
    void testSubtitleExtraction() {
        // 测试字幕提取功能
    }
    
    @Test
    void testVoiceSynthesis() {
        // 测试语音合成功能
    }
    
    @Test
    void testErrorRecovery() {
        // 测试错误恢复机制
    }
}
```

## 风险评估和缓解策略

### 技术风险
1. **外部服务依赖**: 阿里云/Minimax服务不稳定
   - **缓解**: 实现重试机制和服务商故障转移

2. **处理时间长**: 视频处理可能耗时较长  
   - **缓解**: 异步处理+进度通知+超时控制

3. **资源消耗**: 大视频文件处理占用资源
   - **缓解**: 并发控制+资源监控+清理机制

### 业务风险
1. **服务质量**: 翻译和语音质量不稳定
   - **缓解**: 质量评估+人工审核+用户反馈机制

2. **成本控制**: 外部服务调用成本
   - **缓解**: 使用量监控+成本预警+限流机制

## 总结

本设计方案基于现有统一任务系统架构，采用单任务流水线处理模式，实现声音库视频翻译功能。方案遵循SOLID原则和KISS原则，确保系统的可维护性和可扩展性。

**核心优势**:
- 与现有架构高度契合，开发成本低
- 流程清晰，便于监控和调试  
- 支持故障恢复和重试机制
- 模块化设计，便于单独测试和维护

**下一步行动**:
1. 确认技术方案和实现细节
2. 准备开发环境和依赖服务
3. 按阶段实施开发计划
4. 进行充分的测试验证

请确认此方案是否符合您的需求，我将根据您的反馈进入具体的实现阶段。