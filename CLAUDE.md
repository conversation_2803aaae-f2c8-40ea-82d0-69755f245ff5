# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a microservices-based "点点设计" (DianDian Design) platform built with Spring Boot 3.0.13, Spring Cloud 2022.0.2, and Java 21. The system provides AI-powered digital services including image generation, video processing, text services, and digital avatar/voice cloning capabilities.

## Key Architecture

**Microservices Architecture:**
- `nacos-gateway-server`: API Gateway for routing and load balancing
- `nacos-user-server`: User authentication and community features
- `nacos-admin-server`: Administrative platform
- `nacos-draw-server`: AI image generation and processing
- `nacos-text-server`: Text processing and GPT services
- `nacos-task-server`: Scheduled task management
- `nacos-webhook-server`: Third-party webhook handling
- `nacos-course-server`: Course content management
- `nacos-digital-server`: **Core digital avatar and voice services** (main focus)
- `nacos-fcsuser-server`: Formula user services

**Digital Module Architecture (Refactored 2025-07-22):**
- **Admin Package** (`/admin/v1/`): Data management layer with CRUD operations
- **Business Package** (`/api/v1/`): Business logic layer with modular controllers
  - Avatar: Digital avatar business logic
  - Media: Audio/Video processing
  - Task: Task management
  - System: System utilities
- **Legacy Compatibility Layer**: Maintains backward compatibility

## Common Development Commands

**Build and Run:**
```bash
# Build all modules
mvn clean install

# Run specific service (example: digital-server)
cd nacos-digital-server
mvn spring-boot:run

# Build with specific profile
mvn clean install -P dev
mvn clean install -P test
mvn clean install -P prod
```

**Docker Deployment:**
```bash
# Start Nacos (service discovery)
docker-compose -f nacos-digital-server/src/main/resources/dnacos-docker-compose.yml up -d

# Each service has its own docker-compose file
```

**Database:**
- Uses MyBatis Plus 3.5.6 with MySQL 8.3.0
- Connection pool: Druid 1.2.22
- Logical deletion enabled (is_deleted field)

## Project Structure and Naming Conventions

**Entity Layers:**
- `po/`: Database entities (corresponds to table structure) 
- `vo/`: View objects (returned to frontend)
- `dto/`: Data transfer objects (receive frontend parameters)
- `bo/`: Business objects (internal business logic)
- `service/`: Business logic layer
- `controller/`: Request handling layer
- `mapper/`: Data access layer (MyBatis)

**Package Structure:**
- `com.nacos.*`: Main business packages
- `com.business.*`: Shared business utilities (common-business-plus)

## Technology Stack Details

- **Spring Boot**: 3.0.13
- **Spring Cloud**: 2022.0.2  
- **Spring Cloud Alibaba**: 2022.0.0.0-RC2
- **Java**: 21
- **Nacos**: 2.2.1 (Service registry and config center)
- **Database**: MySQL with MyBatis Plus 3.5.6
- **API Documentation**: SpringDoc OpenAPI (Swagger)
- **File Storage**: Alibaba Cloud OSS

## Digital Module Specific Features

**Universal Task System (Strategy + Factory + Redis MQ):**
- Supports video translation, audio generation, and video editing
- Redis message queue based task processing
- Configurable providers and fallback mechanisms
- Located in: `universal-task` configuration section

**Audio Processing Architecture v2.0:**
- Multi-provider support (MiniMax, Microsoft Azure)
- Factory pattern with `AudioProviderProcessorFactory`
- Thread-safe with high performance (170ns processor lookup)
- Supports both sync and async audio generation

**Video Translation:**
- Multi-provider architecture (Lingyang, Aliyun)
- Task status tracking with automatic fallback
- Supports various languages and voice types

## Configuration Profiles

- `dev`: Development environment (default)
- `test`: Testing environment  
- `prod`: Production environment
- `dev-tzh`: Developer-specific configuration

## API Documentation

- Swagger UI: http://************:7000/webjars/swagger-ui/index.html
- API Docs: http://************:7000/fcsai-user/v3/api-docs
- Nacos Console: http://**************:8848/nacos/

## Key Configuration Files

- **Application configs**: `application-{profile}.yml` in each service
- **Docker configs**: `dnacos-docker-compose.yml` in each service resources
- **Logging**: Uses logback-spring.xml with custom log levels

## Development Tips

**Working with Digital Module:**
- The digital module has been refactored with a modular architecture
- Legacy APIs are maintained through delegation pattern
- New features should use the `/api/v1/` endpoints
- Admin operations use `/admin/v1/` endpoints

**Task System:**
- Use the universal task system for new async operations
- Task processors follow strategy pattern
- Redis queues handle message distribution
- Fallback schedulers ensure reliability

**Database Operations:**
- All entities extend BaseEntity or BaseDeleteEntity
- Use logical deletion (is_deleted field)
- Follow MyBatis Plus conventions for CRUD operations

## Security & Authentication

- JWT-based authentication (24-hour expiration)
- Authorization interceptors with whitelist support
- Rate limiting for audio generation endpoints
- Input validation and sanitization