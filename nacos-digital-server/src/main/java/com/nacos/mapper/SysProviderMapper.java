package com.nacos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nacos.entity.po.SysProviderPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 通用服务商配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Mapper
public interface SysProviderMapper extends BaseMapper<SysProviderPO> {

    /**
     * 查询所有启用的服务商，按优先级降序排列
     */
    @Select("SELECT * FROM yhc_sys_provider WHERE enabled = 1 ORDER BY priority DESC, code ASC")
    List<SysProviderPO> selectEnabledProviders();

    /**
     * 查询高优先级服务商（优先级>=80）
     */
    @Select("SELECT * FROM yhc_sys_provider WHERE enabled = 1 AND priority >= 80 ORDER BY priority DESC")
    List<SysProviderPO> selectHighPriorityProviders();

    /**
     * 根据服务商代码列表查询服务商信息
     */
    @Select("<script>" +
            "SELECT * FROM yhc_sys_provider " +
            "WHERE enabled = 1 AND code IN " +
            "<foreach collection='codes' item='code' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            "ORDER BY priority DESC" +
            "</script>")
    List<SysProviderPO> selectByProviderCodes(@Param("codes") List<String> codes);

    /**
     * 根据名称模糊查询服务商
     */
    @Select("SELECT * FROM yhc_sys_provider " +
            "WHERE enabled = 1 " +
            "AND (name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY priority DESC")
    List<SysProviderPO> selectByNameLike(@Param("keyword") String keyword);

    /**
     * 统计启用的服务商数量
     */
    @Select("SELECT COUNT(*) FROM yhc_sys_provider WHERE enabled = 1")
    int countEnabledProviders();

    /**
     * 检查服务商代码是否存在且启用
     */
    @Select("SELECT COUNT(*) > 0 FROM yhc_sys_provider WHERE code = #{code} AND enabled = 1")
    boolean existsEnabledProvider(@Param("code") String code);

    /**
     * 获取最高优先级的服务商
     */
    @Select("SELECT * FROM yhc_sys_provider WHERE enabled = 1 ORDER BY priority DESC LIMIT 1")
    SysProviderPO selectTopPriorityProvider();

    /**
     * 批量更新服务商启用状态
     */
    @Select("<script>" +
            "UPDATE yhc_sys_provider SET enabled = #{enabled}, updated_time = NOW() " +
            "WHERE code IN " +
            "<foreach collection='codes' item='code' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            "</script>")
    int updateEnabledStatus(@Param("codes") List<String> codes, @Param("enabled") Integer enabled);

    /**
     * 更新服务商优先级
     */
    @Select("UPDATE yhc_sys_provider SET priority = #{priority}, updated_time = NOW() WHERE code = #{code}")
    int updatePriority(@Param("code") String code, @Param("priority") Integer priority);
}
