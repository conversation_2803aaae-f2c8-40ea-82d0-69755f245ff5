package com.nacos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nacos.entity.po.SysLanguagePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 通用语言配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Mapper
public interface SysLanguageMapper extends BaseMapper<SysLanguagePO> {

    /**
     * 查询所有启用的语言，按排序权重降序排列
     */
    @Select("SELECT * FROM yhc_sys_language WHERE enabled = 1 ORDER BY sort_order DESC, code ASC")
    List<SysLanguagePO> selectEnabledLanguages();

    /**
     * 查询热门语言（排序权重>=50）
     */
    @Select("SELECT * FROM yhc_sys_language WHERE enabled = 1 AND sort_order >= 50 ORDER BY sort_order DESC")
    List<SysLanguagePO> selectPopularLanguages();

    /**
     * 根据语言代码列表查询语言信息
     */
    @Select("<script>" +
            "SELECT * FROM yhc_sys_language " +
            "WHERE enabled = 1 AND code IN " +
            "<foreach collection='codes' item='code' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            "ORDER BY sort_order DESC" +
            "</script>")
    List<SysLanguagePO> selectByLanguageCodes(@Param("codes") List<String> codes);

    /**
     * 根据名称模糊查询语言
     */
    @Select("SELECT * FROM yhc_sys_language " +
            "WHERE enabled = 1 " +
            "AND (name LIKE CONCAT('%', #{keyword}, '%') OR english_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY sort_order DESC")
    List<SysLanguagePO> selectByNameLike(@Param("keyword") String keyword);

    /**
     * 统计启用的语言数量
     */
    @Select("SELECT COUNT(*) FROM yhc_sys_language WHERE enabled = 1")
    int countEnabledLanguages();

    /**
     * 检查语言代码是否存在且启用
     */
    @Select("SELECT COUNT(*) > 0 FROM yhc_sys_language WHERE code = #{code} AND enabled = 1")
    boolean existsEnabledLanguage(@Param("code") String code);

    /**
     * 批量更新语言启用状态
     */
    @Select("<script>" +
            "UPDATE yhc_sys_language SET enabled = #{enabled}, updated_time = NOW() " +
            "WHERE code IN " +
            "<foreach collection='codes' item='code' open='(' separator=',' close=')'>" +
            "#{code}" +
            "</foreach>" +
            "</script>")
    int updateEnabledStatus(@Param("codes") List<String> codes, @Param("enabled") Integer enabled);
}
