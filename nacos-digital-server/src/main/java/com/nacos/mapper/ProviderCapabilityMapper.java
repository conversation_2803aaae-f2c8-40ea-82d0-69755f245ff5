package com.nacos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nacos.entity.po.ProviderCapabilityPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 视频翻译语种支持配置Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Mapper
public interface ProviderCapabilityMapper extends BaseMapper<ProviderCapabilityPO> {

    /**
     * 根据语言对查询支持的视频翻译服务商
     */
    @Select("SELECT vls.* FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.source_language = #{sourceLanguage} " +
            "AND vls.target_language = #{targetLanguage} " +
            "AND vls.enabled = 1 AND p.enabled = 1 " +
            "ORDER BY p.priority DESC, vls.quality_level DESC")
    List<ProviderCapabilityPO> selectByLanguagePair(
            @Param("sourceLanguage") String sourceLanguage,
            @Param("targetLanguage") String targetLanguage);

    /**
     * 查询所有支持的视频翻译语言对
     */
    @Select("SELECT DISTINCT vls.source_language, vls.target_language " +
            "FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.enabled = 1 AND p.enabled = 1 " +
            "ORDER BY vls.source_language, vls.target_language")
    List<ProviderCapabilityPO> selectAllLanguagePairs();

    /**
     * 根据服务商代码查询其支持的所有视频翻译语种
     */
    @Select("SELECT * FROM yhc_video_translation_language_support " +
            "WHERE provider_code = #{providerCode} AND enabled = 1 " +
            "ORDER BY source_language, target_language")
    List<ProviderCapabilityPO> selectByProviderCode(@Param("providerCode") String providerCode);

    /**
     * 根据源语言查询支持的目标语言
     */
    @Select("SELECT DISTINCT vls.target_language " +
            "FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.source_language = #{sourceLanguage} " +
            "AND vls.enabled = 1 AND p.enabled = 1 " +
            "ORDER BY vls.target_language")
    List<String> selectTargetLanguagesBySource(@Param("sourceLanguage") String sourceLanguage);

    /**
     * 获取最佳视频翻译服务商（按优先级排序）
     */
    @Select("SELECT vls.* FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.source_language = #{sourceLanguage} " +
            "AND vls.target_language = #{targetLanguage} " +
            "AND vls.enabled = 1 AND p.enabled = 1 " +
            "ORDER BY p.priority DESC " +
            "LIMIT 1")
    ProviderCapabilityPO selectBestProvider(
            @Param("sourceLanguage") String sourceLanguage,
            @Param("targetLanguage") String targetLanguage);

    /**
     * 根据服务商代码和语言对查询配置
     */
    @Select("SELECT * FROM yhc_video_translation_language_support " +
            "WHERE provider_code = #{providerCode} " +
            "AND source_language = #{sourceLanguage} " +
            "AND target_language = #{targetLanguage} " +
            "AND enabled = 1")
    ProviderCapabilityPO selectByProviderAndLanguagePair(
            @Param("providerCode") String providerCode,
            @Param("sourceLanguage") String sourceLanguage,
            @Param("targetLanguage") String targetLanguage);

    /**
     * 查询所有启用的视频翻译服务商配置
     */
    @Select("SELECT vls.* FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.enabled = 1 AND p.enabled = 1 " +
            "ORDER BY p.priority DESC")
    List<ProviderCapabilityPO> selectAllEnabledProviders();

    /**
     * 统计视频翻译语种支持数量
     */
    @Select("SELECT COUNT(*) FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.enabled = 1 AND p.enabled = 1")
    int countSupportedLanguagePairs();

    /**
     * 检查语言对是否被支持
     */
    @Select("SELECT COUNT(*) > 0 FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.source_language = #{sourceLanguage} " +
            "AND vls.target_language = #{targetLanguage} " +
            "AND vls.enabled = 1 AND p.enabled = 1")
    boolean isLanguagePairSupported(
            @Param("sourceLanguage") String sourceLanguage,
            @Param("targetLanguage") String targetLanguage);

    /**
     * 批量更新启用状态
     */
    @Select("<script>" +
            "UPDATE yhc_video_translation_language_support SET enabled = #{enabled}, updated_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int updateEnabledStatus(@Param("ids") List<Long> ids, @Param("enabled") Integer enabled);

    /**
     * 更新服务商语言代码映射
     */
    @Select("UPDATE yhc_video_translation_language_support " +
            "SET provider_source_code = #{providerSourceCode}, provider_target_code = #{providerTargetCode}, updated_time = NOW() " +
            "WHERE id = #{id}")
    int updateProviderLanguageCodes(@Param("id") Long id,
                                   @Param("providerSourceCode") String providerSourceCode,
                                   @Param("providerTargetCode") String providerTargetCode);
}
