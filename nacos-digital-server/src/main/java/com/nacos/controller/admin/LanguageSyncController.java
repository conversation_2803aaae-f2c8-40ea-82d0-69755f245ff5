package com.nacos.controller.admin;

import com.nacos.entity.po.ProviderCapabilityPO;
import com.nacos.result.Result;
import com.nacos.service.LanguageSupportSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 语种同步管理控制器
 * 提供语种支持数据的同步管理功能
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Tag(name = "语种同步管理", description = "语种支持数据同步管理接口")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/v1/language/sync")
public class LanguageSyncController {

    private final LanguageSupportSyncService languageSupportSyncService;

    /**
     * 手动同步所有服务商的语种支持数据
     * URL: /admin/v1/language/sync/all
     * Method: POST
     *
     * @return 同步结果
     */
    @Operation(summary = "手动同步所有服务商语种支持数据", description = "触发所有服务商的语种支持数据同步")
    @PostMapping("/all")
    public Result<String> syncAllLanguageSupport() {
        String methodName = "syncAllLanguageSupport";
        log.info("[{}] 管理员手动触发所有服务商语种同步", methodName);
        
        try {
            Result<String> result = languageSupportSyncService.syncAllLanguageSupport();
            log.info("[{}] 所有服务商语种同步完成: {}", methodName, result.getMessage());
            return result;
        } catch (Exception e) {
            log.error("[{}] 所有服务商语种同步异常", methodName, e);
            return Result.ERROR("同步失败: " + e.getMessage());
        }
    }

    /**
     * 手动同步LINGYANG服务商语种支持数据
     * URL: /admin/v1/language/sync/lingyang
     * Method: POST
     *
     * @return 同步结果
     */
    @Operation(summary = "手动同步LINGYANG语种支持数据", description = "触发LINGYANG服务商的语种支持数据同步")
    @PostMapping("/lingyang")
    public Result<String> syncLingyangLanguageSupport() {
        String methodName = "syncLingyangLanguageSupport";
        log.info("[{}] 管理员手动触发LINGYANG语种同步", methodName);
        
        try {
            Result<String> result = languageSupportSyncService.syncLingyangLanguageSupport();
            log.info("[{}] LINGYANG语种同步完成: {}", methodName, result.getMessage());
            return result;
        } catch (Exception e) {
            log.error("[{}] LINGYANG语种同步异常", methodName, e);
            return Result.ERROR("同步失败: " + e.getMessage());
        }
    }

    /**
     * 根据服务商代码同步语种支持数据
     * URL: /admin/v1/language/sync/provider/{providerCode}
     * Method: POST
     *
     * @param providerCode 服务商代码
     * @return 同步结果
     */
    @Operation(summary = "根据服务商代码同步语种支持数据", description = "触发指定服务商的语种支持数据同步")
    @PostMapping("/provider/{providerCode}")
    public Result<String> syncLanguageSupportByProvider(
            @Parameter(description = "服务商代码", example = "LINGYANG")
            @PathVariable String providerCode) {
        String methodName = "syncLanguageSupportByProvider";
        log.info("[{}] 管理员手动触发服务商[{}]语种同步", methodName, providerCode);
        
        try {
            Result<String> result = languageSupportSyncService.syncLanguageSupportByProvider(providerCode);
            log.info("[{}] 服务商[{}]语种同步完成: {}", methodName, providerCode, result.getMessage());
            return result;
        } catch (Exception e) {
            log.error("[{}] 服务商[{}]语种同步异常", methodName, providerCode, e);
            return Result.ERROR("同步失败: " + e.getMessage());
        }
    }

    /**
     * 获取同步状态统计
     * URL: /admin/v1/language/sync/status
     * Method: GET
     *
     * @return 同步状态统计信息
     */
    @Operation(summary = "获取同步状态统计", description = "获取语种支持数据的同步状态统计信息")
    @GetMapping("/status")
    public Result<Map<String, Object>> getSyncStatusStatistics() {
        String methodName = "getSyncStatusStatistics";
        log.info("[{}] 获取语种同步状态统计", methodName);
        
        try {
            Map<String, Object> statistics = languageSupportSyncService.getSyncStatusStatistics();
            log.info("[{}] 语种同步状态统计获取完成", methodName);
            return Result.SUCCESS(statistics);
        } catch (Exception e) {
            log.error("[{}] 获取语种同步状态统计异常", methodName, e);
            return Result.ERROR("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定服务商的同步状态统计
     * URL: /admin/v1/language/sync/status/{providerCode}
     * Method: GET
     *
     * @param providerCode 服务商代码
     * @return 服务商同步状态统计信息
     */
    @Operation(summary = "获取指定服务商的同步状态统计", description = "获取指定服务商的语种支持数据同步状态统计")
    @GetMapping("/status/{providerCode}")
    public Result<Map<String, Object>> getSyncStatusStatisticsByProvider(
            @Parameter(description = "服务商代码", example = "LINGYANG")
            @PathVariable String providerCode) {
        String methodName = "getSyncStatusStatisticsByProvider";
        log.info("[{}] 获取服务商[{}]语种同步状态统计", methodName, providerCode);
        
        try {
            Map<String, Object> statistics = languageSupportSyncService.getSyncStatusStatisticsByProvider(providerCode);
            log.info("[{}] 服务商[{}]语种同步状态统计获取完成", methodName, providerCode);
            return Result.SUCCESS(statistics);
        } catch (Exception e) {
            log.error("[{}] 获取服务商[{}]语种同步状态统计异常", methodName, providerCode, e);
            return Result.ERROR("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的语种对列表
     * URL: /admin/v1/language/sync/language-pairs
     * Method: GET
     *
     * @param providerCode 服务商代码（可选）
     * @return 语种对列表
     */
    @Operation(summary = "获取支持的语种对列表", description = "获取支持的语种对列表，可按服务商过滤")
    @GetMapping("/language-pairs")
    public Result<List<ProviderCapabilityPO>> getSupportedLanguagePairs(
            @Parameter(description = "服务商代码（可选）", example = "LINGYANG")
            @RequestParam(required = false) String providerCode) {
        String methodName = "getSupportedLanguagePairs";
        log.info("[{}] 获取支持的语种对列表, 服务商: {}", methodName, providerCode);
        
        try {
            List<ProviderCapabilityPO> languagePairs = languageSupportSyncService.getSupportedLanguagePairs(providerCode);
            log.info("[{}] 获取到{}个支持的语种对", methodName, languagePairs.size());
            return Result.SUCCESS(languagePairs);
        } catch (Exception e) {
            log.error("[{}] 获取支持的语种对列表异常", methodName, e);
            return Result.ERROR("获取语种对列表失败: " + e.getMessage());
        }
    }

    /**
     * 重试失败的同步记录
     * URL: /admin/v1/language/sync/retry
     * Method: POST
     *
     * @return 重试结果
     */
    @Operation(summary = "重试失败的同步记录", description = "重试所有失败的语种同步记录")
    @PostMapping("/retry")
    public Result<String> retryFailedSyncs() {
        String methodName = "retryFailedSyncs";
        log.info("[{}] 管理员手动触发失败同步记录重试", methodName);
        
        try {
            Result<String> result = languageSupportSyncService.retryFailedSyncs();
            log.info("[{}] 失败同步记录重试完成: {}", methodName, result.getMessage());
            return result;
        } catch (Exception e) {
            log.error("[{}] 失败同步记录重试异常", methodName, e);
            return Result.ERROR("重试失败: " + e.getMessage());
        }
    }

    /**
     * 重试指定服务商的失败同步记录
     * URL: /admin/v1/language/sync/retry/{providerCode}
     * Method: POST
     *
     * @param providerCode 服务商代码
     * @return 重试结果
     */
    @Operation(summary = "重试指定服务商的失败同步记录", description = "重试指定服务商的失败语种同步记录")
    @PostMapping("/retry/{providerCode}")
    public Result<String> retryFailedSyncsByProvider(
            @Parameter(description = "服务商代码", example = "LINGYANG")
            @PathVariable String providerCode) {
        String methodName = "retryFailedSyncsByProvider";
        log.info("[{}] 管理员手动触发服务商[{}]失败同步记录重试", methodName, providerCode);
        
        try {
            Result<String> result = languageSupportSyncService.retryFailedSyncsByProvider(providerCode);
            log.info("[{}] 服务商[{}]失败同步记录重试完成: {}", methodName, providerCode, result.getMessage());
            return result;
        } catch (Exception e) {
            log.error("[{}] 服务商[{}]失败同步记录重试异常", methodName, providerCode, e);
            return Result.ERROR("重试失败: " + e.getMessage());
        }
    }

    /**
     * 刷新语种支持缓存
     * URL: /admin/v1/language/sync/refresh-cache
     * Method: POST
     *
     * @return 刷新结果
     */
    @Operation(summary = "刷新语种支持缓存", description = "强制刷新语种支持数据缓存")
    @PostMapping("/refresh-cache")
    public Result<String> refreshLanguageSupportCache() {
        String methodName = "refreshLanguageSupportCache";
        log.info("[{}] 管理员手动触发语种支持缓存刷新", methodName);
        
        try {
            Result<String> result = languageSupportSyncService.refreshLanguageSupportCache();
            log.info("[{}] 语种支持缓存刷新完成: {}", methodName, result.getMessage());
            return result;
        } catch (Exception e) {
            log.error("[{}] 语种支持缓存刷新异常", methodName, e);
            return Result.ERROR("缓存刷新失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据一致性
     * URL: /admin/v1/language/sync/check-consistency
     * Method: GET
     *
     * @return 一致性检查结果
     */
    @Operation(summary = "检查数据一致性", description = "检查语种支持数据的一致性")
    @GetMapping("/check-consistency")
    public Result<Map<String, Object>> checkDataConsistency() {
        String methodName = "checkDataConsistency";
        log.info("[{}] 管理员触发数据一致性检查", methodName);
        
        try {
            Map<String, Object> result = languageSupportSyncService.checkDataConsistency();
            log.info("[{}] 数据一致性检查完成", methodName);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("[{}] 数据一致性检查异常", methodName, e);
            return Result.ERROR("一致性检查失败: " + e.getMessage());
        }
    }
}
