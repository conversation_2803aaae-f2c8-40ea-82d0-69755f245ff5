package com.nacos.entity.bo;

import com.nacos.entity.enums.SyncStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步结果业务对象
 * 用于记录语种支持数据同步的详细结果信息
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Schema(description = "同步结果信息")
public class SyncResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 同步是否成功
     */
    @Schema(description = "同步是否成功", example = "true")
    private Boolean success;

    /**
     * 同步状态
     */
    @Schema(description = "同步状态")
    private SyncStatus status;

    /**
     * 服务商代码
     */
    @Schema(description = "服务商代码", example = "LINGYANG")
    private String providerCode;

    /**
     * 同步开始时间
     */
    @Schema(description = "同步开始时间")
    private LocalDateTime startTime;

    /**
     * 同步结束时间
     */
    @Schema(description = "同步结束时间")
    private LocalDateTime endTime;

    /**
     * 同步耗时（毫秒）
     */
    @Schema(description = "同步耗时（毫秒）", example = "5000")
    private Long durationMs;

    /**
     * 总处理记录数
     */
    @Schema(description = "总处理记录数", example = "100")
    private Integer totalCount;

    /**
     * 新增记录数
     */
    @Schema(description = "新增记录数", example = "80")
    private Integer newCount;

    /**
     * 更新记录数
     */
    @Schema(description = "更新记录数", example = "15")
    private Integer updatedCount;

    /**
     * 跳过记录数
     */
    @Schema(description = "跳过记录数", example = "5")
    private Integer skippedCount;

    /**
     * 错误记录数
     */
    @Schema(description = "错误记录数", example = "0")
    private Integer errorCount;

    /**
     * 同步消息
     */
    @Schema(description = "同步消息", example = "LINGYANG语种同步成功")
    private String message;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 详细错误列表
     */
    @Schema(description = "详细错误列表")
    @Builder.Default
    private List<String> errorDetails = new ArrayList<>();

    /**
     * 同步统计信息
     */
    @Schema(description = "同步统计信息")
    private String statisticsInfo;

    /**
     * 扩展信息
     */
    @Schema(description = "扩展信息")
    private String extendInfo;

    // ==================== 业务方法 ====================

    /**
     * 创建成功的同步结果
     */
    public static SyncResult success(String providerCode, String message) {
        return SyncResult.builder()
                .success(true)
                .status(SyncStatus.SUCCESS)
                .providerCode(providerCode)
                .message(message)
                .endTime(LocalDateTime.now())
                .totalCount(0)
                .newCount(0)
                .updatedCount(0)
                .skippedCount(0)
                .errorCount(0)
                .build();
    }

    /**
     * 创建失败的同步结果
     */
    public static SyncResult failure(String providerCode, String errorMessage) {
        return SyncResult.builder()
                .success(false)
                .status(SyncStatus.FAILED)
                .providerCode(providerCode)
                .message("同步失败")
                .errorMessage(errorMessage)
                .endTime(LocalDateTime.now())
                .totalCount(0)
                .newCount(0)
                .updatedCount(0)
                .skippedCount(0)
                .errorCount(0)
                .build();
    }

    /**
     * 创建进行中的同步结果
     */
    public static SyncResult inProgress(String providerCode) {
        return SyncResult.builder()
                .success(null)
                .status(SyncStatus.IN_PROGRESS)
                .providerCode(providerCode)
                .message("同步进行中")
                .startTime(LocalDateTime.now())
                .totalCount(0)
                .newCount(0)
                .updatedCount(0)
                .skippedCount(0)
                .errorCount(0)
                .build();
    }

    /**
     * 设置统计信息
     */
    public SyncResult withStatistics(int totalCount, int newCount, int updatedCount, int skippedCount, int errorCount) {
        this.totalCount = totalCount;
        this.newCount = newCount;
        this.updatedCount = updatedCount;
        this.skippedCount = skippedCount;
        this.errorCount = errorCount;
        return this;
    }

    /**
     * 设置时间信息
     */
    public SyncResult withTiming(LocalDateTime startTime, LocalDateTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
        if (startTime != null && endTime != null) {
            this.durationMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
        return this;
    }

    /**
     * 添加错误详情
     */
    public SyncResult addErrorDetail(String errorDetail) {
        if (this.errorDetails == null) {
            this.errorDetails = new ArrayList<>();
        }
        this.errorDetails.add(errorDetail);
        return this;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        int successCount = (newCount != null ? newCount : 0) + 
                          (updatedCount != null ? updatedCount : 0) + 
                          (skippedCount != null ? skippedCount : 0);
        return (double) successCount / totalCount * 100;
    }

    /**
     * 获取错误率
     */
    public double getErrorRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) (errorCount != null ? errorCount : 0) / totalCount * 100;
    }

    /**
     * 检查是否有错误
     */
    public boolean hasErrors() {
        return errorCount != null && errorCount > 0;
    }

    /**
     * 检查是否完全成功
     */
    public boolean isCompleteSuccess() {
        return success != null && success && !hasErrors();
    }

    /**
     * 获取格式化的统计信息
     */
    public String getFormattedStatistics() {
        if (totalCount == null) {
            return "暂无统计信息";
        }
        
        return String.format("总计:%d, 新增:%d, 更新:%d, 跳过:%d, 错误:%d, 成功率:%.1f%%",
                totalCount,
                newCount != null ? newCount : 0,
                updatedCount != null ? updatedCount : 0,
                skippedCount != null ? skippedCount : 0,
                errorCount != null ? errorCount : 0,
                getSuccessRate());
    }

    /**
     * 获取格式化的耗时信息
     */
    public String getFormattedDuration() {
        if (durationMs == null) {
            return "未知";
        }
        
        if (durationMs < 1000) {
            return durationMs + "毫秒";
        } else if (durationMs < 60000) {
            return String.format("%.1f秒", durationMs / 1000.0);
        } else {
            return String.format("%.1f分钟", durationMs / 60000.0);
        }
    }

    @Override
    public String toString() {
        return String.format("SyncResult{provider='%s', status=%s, %s, 耗时:%s}",
                providerCode, status, getFormattedStatistics(), getFormattedDuration());
    }
}
