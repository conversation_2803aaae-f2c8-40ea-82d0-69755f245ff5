package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 视频翻译请求DTO（多服务商通用版本）
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@Schema(description = "视频翻译请求参数")
public class VideoTranslateRequestDTO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", required = true, example = "user123")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 视频文件（必填）
     */
    @Schema(description = "视频文件", required = true)
    @NotNull(message = "视频文件不能为空")
    private MultipartFile file;

    /**
     * 源语言代码
     */
    @Schema(description = "源语言代码", required = true, example = "cn")
    @NotBlank(message = "源语言不能为空")
    private String sourceLanguage;

    /**
     * 目标语言代码
     */
    @Schema(description = "目标语言代码", required = true, example = "en")
    @NotBlank(message = "目标语言不能为空")
    private String targetLanguage;

    /**
     * 音色ID（当useOriginalVoice为false时必填）
     */
    @Schema(description = "音色ID", required = false, example = "246")
    private String voiceId;

    /**
     * 任务名称（可选）
     */
    @Schema(description = "任务名称", required = false, example = "我的视频翻译任务")
    @Size(max = 100, message = "任务名称不能超过100个字符")
    private String taskName;

    /**
     * 是否使用原声（true-使用视频原声，false-使用声音库）
     */
    @Schema(description = "是否使用原声", required = false, example = "false")
    private Boolean useOriginalVoice = false;

    /**
     * 扩展参数（用于服务商特有参数，JSON格式）
     */
    @Schema(description = "扩展参数，用于服务商特有参数，JSON格式", required = false, example = "{\"customParam\":\"value\"}")
    private String extParams;

    /**
     * 视频文件URL
     * 注意：此字段由系统自动设置为上传后的文件URL，前端无需传递
     */
    @Schema(description = "视频文件URL（系统自动设置，前端无需传递）",
            required = false,
            example = "https://example.com/video.mp4")
    private String videoUrl;

    // ==================== 业务方法 ====================

    /**
     * 验证源语言和目标语言是否相同
     */
    public boolean isSameLanguage() {
        return sourceLanguage != null && sourceLanguage.equals(targetLanguage);
    }

    /**
     * 获取语言转换描述
     */
    public String getLanguagePairDescription() {
        return sourceLanguage + " → " + targetLanguage;
    }

    /**
     * 获取默认任务名称
     */
    public String getDefaultTaskName() {
        if (taskName != null && !taskName.trim().isEmpty()) {
            return taskName;
        }
        return "视频翻译任务 - " + getLanguagePairDescription();
    }

    /**
     * 验证voiceId是否必填
     */
    public boolean isVoiceIdRequired() {
        return useOriginalVoice == null || !useOriginalVoice;
    }

    /**
     * 验证参数完整性
     */
    public boolean isValid() {
        // 基础参数验证（videoUrl由系统自动设置，无需验证）
        if (userId == null || userId.trim().isEmpty() ||
            sourceLanguage == null || sourceLanguage.trim().isEmpty() ||
            targetLanguage == null || targetLanguage.trim().isEmpty()) {
            return false;
        }

        // 语言不能相同
        if (isSameLanguage()) {
            return false;
        }

        // 如果不使用原声，voiceId必填
        if (isVoiceIdRequired() && (voiceId == null || voiceId.trim().isEmpty())) {
            return false;
        }

        return true;
    }

    /**
     * 验证参数完整性（包含videoUrl验证，用于服务层调用）
     */
    public boolean isValidWithVideoUrl() {
        // 先验证基础参数
        if (!isValid()) {
            return false;
        }

        // 验证videoUrl（服务层设置后验证）
        if (videoUrl == null || videoUrl.trim().isEmpty()) {
            return false;
        }

        return true;
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (useOriginalVoice == null) {
            useOriginalVoice = false;
        }
    }
}
