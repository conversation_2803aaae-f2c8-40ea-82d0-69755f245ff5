package com.nacos.entity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 同步状态枚举
 * 定义语种支持数据同步的各种状态
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Schema(description = "同步状态枚举")
public enum SyncStatus {

    /**
     * 等待中 - 同步任务已创建但尚未开始执行
     */
    PENDING("PENDING", "等待中", "同步任务已创建，等待执行"),

    /**
     * 进行中 - 同步任务正在执行
     */
    IN_PROGRESS("IN_PROGRESS", "进行中", "同步任务正在执行中"),

    /**
     * 成功 - 同步任务执行成功
     */
    SUCCESS("SUCCESS", "成功", "同步任务执行成功"),

    /**
     * 失败 - 同步任务执行失败
     */
    FAILED("FAILED", "失败", "同步任务执行失败"),

    /**
     * 部分成功 - 同步任务部分成功，存在一些错误但不影响整体结果
     */
    PARTIAL_SUCCESS("PARTIAL_SUCCESS", "部分成功", "同步任务部分成功，存在少量错误"),

    /**
     * 已取消 - 同步任务被手动取消
     */
    CANCELLED("CANCELLED", "已取消", "同步任务被手动取消"),

    /**
     * 超时 - 同步任务执行超时
     */
    TIMEOUT("TIMEOUT", "超时", "同步任务执行超时"),

    /**
     * 重试中 - 同步任务失败后正在重试
     */
    RETRYING("RETRYING", "重试中", "同步任务失败后正在重试"),

    /**
     * 跳过 - 同步任务被跳过（如数据已是最新）
     */
    SKIPPED("SKIPPED", "跳过", "同步任务被跳过，数据已是最新");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String description;

    SyncStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举值
     * 
     * @param code 状态代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static SyncStatus fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (SyncStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查是否为最终状态（不会再变化的状态）
     * 
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || 
               this == FAILED || 
               this == PARTIAL_SUCCESS || 
               this == CANCELLED || 
               this == TIMEOUT ||
               this == SKIPPED;
    }

    /**
     * 检查是否为成功状态
     * 
     * @return 是否为成功状态
     */
    public boolean isSuccessStatus() {
        return this == SUCCESS || this == PARTIAL_SUCCESS || this == SKIPPED;
    }

    /**
     * 检查是否为失败状态
     * 
     * @return 是否为失败状态
     */
    public boolean isFailureStatus() {
        return this == FAILED || this == TIMEOUT || this == CANCELLED;
    }

    /**
     * 检查是否为进行中状态
     * 
     * @return 是否为进行中状态
     */
    public boolean isActiveStatus() {
        return this == PENDING || this == IN_PROGRESS || this == RETRYING;
    }

    /**
     * 检查是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED || this == TIMEOUT;
    }

    /**
     * 检查是否可以取消
     * 
     * @return 是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == IN_PROGRESS || this == RETRYING;
    }

    /**
     * 获取状态的优先级（用于排序）
     * 数值越小优先级越高
     * 
     * @return 优先级数值
     */
    public int getPriority() {
        switch (this) {
            case IN_PROGRESS:
            case RETRYING:
                return 1;
            case PENDING:
                return 2;
            case FAILED:
            case TIMEOUT:
                return 3;
            case CANCELLED:
                return 4;
            case PARTIAL_SUCCESS:
                return 5;
            case SUCCESS:
            case SKIPPED:
                return 6;
            default:
                return 999;
        }
    }

    /**
     * 获取状态对应的颜色（用于前端显示）
     * 
     * @return 颜色代码
     */
    public String getColor() {
        switch (this) {
            case SUCCESS:
            case SKIPPED:
                return "green";
            case PARTIAL_SUCCESS:
                return "orange";
            case FAILED:
            case TIMEOUT:
            case CANCELLED:
                return "red";
            case IN_PROGRESS:
            case RETRYING:
                return "blue";
            case PENDING:
                return "gray";
            default:
                return "gray";
        }
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", name, code);
    }
}
