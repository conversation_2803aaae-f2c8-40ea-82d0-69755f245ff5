package com.nacos.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 语言对VO
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "语言对信息")
public class LanguagePairVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 源语言代码
     */
    @Schema(description = "源语言代码", example = "cn")
    private String sourceLanguage;

    /**
     * 源语言名称
     */
    @Schema(description = "源语言名称", example = "中文(简体)")
    private String sourceLanguageName;

    /**
     * 目标语言代码
     */
    @Schema(description = "目标语言代码", example = "en")
    private String targetLanguage;

    /**
     * 目标语言名称
     */
    @Schema(description = "目标语言名称", example = "英语")
    private String targetLanguageName;

    /**
     * 语言对描述
     */
    @Schema(description = "语言对描述", example = "中文(简体) → 英语")
    private String description;

    /**
     * 支持的服务商数量
     */
    @Schema(description = "支持的服务商数量", example = "2")
    private Integer providerCount;

    /**
     * 是否为热门语言对
     */
    @Schema(description = "是否为热门语言对", example = "true")
    private Boolean isPopular;

    // ==================== 业务方法 ====================

    /**
     * 获取语言对的简短描述
     */
    public String getShortDescription() {
        return sourceLanguage + " → " + targetLanguage;
    }

    /**
     * 获取语言对的完整描述
     */
    public String getFullDescription() {
        if (sourceLanguageName != null && targetLanguageName != null) {
            return sourceLanguageName + " → " + targetLanguageName;
        }
        return getShortDescription();
    }

    /**
     * 检查是否为相同语言
     */
    public boolean isSameLanguage() {
        return sourceLanguage != null && sourceLanguage.equals(targetLanguage);
    }

    /**
     * 检查是否包含指定语言
     */
    public boolean containsLanguage(String languageCode) {
        return (sourceLanguage != null && sourceLanguage.equals(languageCode)) ||
               (targetLanguage != null && targetLanguage.equals(languageCode));
    }

    /**
     * 检查是否为双向语言对（即存在反向语言对）
     */
    public boolean isBidirectional() {
        // 这个方法需要在服务层中结合其他数据来判断
        return false;
    }

    /**
     * 获取反向语言对
     */
    public LanguagePairVO getReversePair() {
        return LanguagePairVO.builder()
                .sourceLanguage(this.targetLanguage)
                .sourceLanguageName(this.targetLanguageName)
                .targetLanguage(this.sourceLanguage)
                .targetLanguageName(this.sourceLanguageName)
                .description(this.targetLanguageName + " → " + this.sourceLanguageName)
                .build();
    }

    /**
     * 检查是否有足够的服务商支持
     */
    public boolean hasAdequateProviderSupport() {
        return providerCount != null && providerCount >= 1;
    }

    /**
     * 检查是否为多服务商支持
     */
    public boolean hasMultipleProviders() {
        return providerCount != null && providerCount > 1;
    }

    @Override
    public String toString() {
        return "LanguagePairVO{" +
                "sourceLanguage='" + sourceLanguage + '\'' +
                ", targetLanguage='" + targetLanguage + '\'' +
                ", description='" + description + '\'' +
                ", providerCount=" + providerCount +
                '}';
    }
}
