package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 语言配置实体类
 * 对应表：yhc_sys_language
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("yhc_sys_language")
@Schema(description = "语言配置实体类")
public class SysLanguagePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标准语言代码(ISO 639-1)
     */
    @Schema(description = "标准语言代码(ISO 639-1)", example = "cn")
    @TableId("code")
    private String code;

    /**
     * 语言名称
     */
    @Schema(description = "语言名称", example = "中文(简体)")
    @TableField("name")
    private String name;

    /**
     * 英文名称
     */
    @Schema(description = "英文名称", example = "Chinese Simplified")
    @TableField("english_name")
    private String englishName;

    /**
     * 排序权重，数值越大越靠前
     */
    @Schema(description = "排序权重，数值越大越靠前", example = "100")
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否启用：1启用 0禁用
     */
    @Schema(description = "是否启用：1启用 0禁用", example = "1")
    @TableField("enabled")
    private Integer enabled;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    // ==================== 业务方法 ====================

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return enabled != null && enabled == 1;
    }

    /**
     * 获取显示名称（优先中文名称）
     */
    public String getDisplayName() {
        return name != null && !name.trim().isEmpty() ? name : englishName;
    }

    /**
     * 获取完整描述
     */
    public String getFullDescription() {
        if (name != null && englishName != null) {
            return name + " (" + englishName + ")";
        }
        return getDisplayName();
    }

    /**
     * 设置启用状态
     */
    public SysLanguagePO setEnabledStatus(boolean enabled) {
        this.enabled = enabled ? 1 : 0;
        return this;
    }

    /**
     * 检查是否为热门语言（根据排序权重判断）
     */
    public boolean isPopular() {
        return sortOrder != null && sortOrder >= 50;
    }

    /**
     * 比较排序权重
     */
    public int compareTo(SysLanguagePO other) {
        if (other == null) {
            return 1;
        }
        
        Integer thisSortOrder = this.sortOrder != null ? this.sortOrder : 0;
        Integer otherSortOrder = other.sortOrder != null ? other.sortOrder : 0;
        
        // 降序排列，权重大的在前
        return otherSortOrder.compareTo(thisSortOrder);
    }

    @Override
    public String toString() {
        return "SysLanguagePO{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", englishName='" + englishName + '\'' +
                ", sortOrder=" + sortOrder +
                ", enabled=" + enabled +
                '}';
    }
}
