package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视频翻译语种支持配置实体类
 * 对应表：yhc_video_translation_language_support
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("yhc_video_translation_language_support")
@Schema(description = "视频翻译语种支持配置实体类")
public class ProviderCapabilityPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务商代码
     */
    @Schema(description = "服务商代码", example = "LINGYANG")
    @TableField("provider_code")
    private String providerCode;

    /**
     * 源语言代码
     */
    @Schema(description = "源语言代码", example = "cn")
    @TableField("source_language")
    private String sourceLanguage;

    /**
     * 目标语言代码
     */
    @Schema(description = "目标语言代码", example = "en")
    @TableField("target_language")
    private String targetLanguage;

    /**
     * 服务商内部源语言代码
     */
    @Schema(description = "服务商内部源语言代码", example = "zh-Hans")
    @TableField("provider_source_code")
    private String providerSourceCode;

    /**
     * 服务商内部目标语言代码
     */
    @Schema(description = "服务商内部目标语言代码", example = "en")
    @TableField("provider_target_code")
    private String providerTargetCode;



    /**
     * 是否启用：1启用 0禁用
     */
    @Schema(description = "是否启用：1启用 0禁用", example = "1")
    @TableField("enabled")
    private Integer enabled;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    // ==================== 业务方法 ====================

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return enabled != null && enabled == 1;
    }

    /**
     * 设置启用状态
     */
    public ProviderCapabilityPO setEnabledStatus(boolean enabled) {
        this.enabled = enabled ? 1 : 0;
        return this;
    }

    /**
     * 获取语言对描述
     */
    public String getLanguagePairDescription() {
        return sourceLanguage + " → " + targetLanguage;
    }

    /**
     * 获取服务商语言对描述
     */
    public String getProviderLanguagePairDescription() {
        String sourceCode = providerSourceCode != null ? providerSourceCode : sourceLanguage;
        String targetCode = providerTargetCode != null ? providerTargetCode : targetLanguage;
        return sourceCode + " → " + targetCode;
    }



    /**
     * 检查语言对是否匹配
     */
    public boolean matchesLanguagePair(String sourceLanguage, String targetLanguage) {
        return this.sourceLanguage != null && this.sourceLanguage.equals(sourceLanguage) &&
               this.targetLanguage != null && this.targetLanguage.equals(targetLanguage);
    }

    /**
     * 检查是否支持视频翻译（专用方法）
     */
    public boolean supportsVideoTranslation() {
        return enabled != null && enabled == 1;
    }

    /**
     * 获取服务商源语言代码（优先使用服务商内部代码）
     */
    public String getEffectiveSourceCode() {
        return providerSourceCode != null && !providerSourceCode.trim().isEmpty()
               ? providerSourceCode : sourceLanguage;
    }

    /**
     * 获取服务商目标语言代码（优先使用服务商内部代码）
     */
    public String getEffectiveTargetCode() {
        return providerTargetCode != null && !providerTargetCode.trim().isEmpty()
               ? providerTargetCode : targetLanguage;
    }

    @Override
    public String toString() {
        return "VideoTranslationLanguageSupportPO{" +
                "id=" + id +
                ", providerCode='" + providerCode + '\'' +
                ", sourceLanguage='" + sourceLanguage + '\'' +
                ", targetLanguage='" + targetLanguage + '\'' +
                ", providerSourceCode='" + providerSourceCode + '\'' +
                ", providerTargetCode='" + providerTargetCode + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
