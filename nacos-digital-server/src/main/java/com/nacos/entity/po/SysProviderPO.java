package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务商配置实体类
 * 对应表：yhc_sys_provider
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("yhc_sys_provider")
@Schema(description = "服务商配置实体类")
public class SysProviderPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务商代码
     */
    @Schema(description = "服务商代码", example = "LINGYANG")
    @TableId("code")
    private String code;

    /**
     * 服务商名称
     */
    @Schema(description = "服务商名称", example = "讯飞羚羊")
    @TableField("name")
    private String name;

    /**
     * 服务商描述
     */
    @Schema(description = "服务商描述", example = "讯飞羚羊视频翻译平台")
    @TableField("description")
    private String description;

    /**
     * 优先级，数值越大优先级越高
     */
    @Schema(description = "优先级，数值越大优先级越高", example = "100")
    @TableField("priority")
    private Integer priority;

    /**
     * 是否启用：1启用 0禁用
     */
    @Schema(description = "是否启用：1启用 0禁用", example = "1")
    @TableField("enabled")
    private Integer enabled;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    // ==================== 业务方法 ====================

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return enabled != null && enabled == 1;
    }

    /**
     * 获取显示名称（优先名称，其次代码）
     */
    public String getDisplayName() {
        return name != null && !name.trim().isEmpty() ? name : code;
    }

    /**
     * 获取完整描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(getDisplayName());
        
        if (description != null && !description.trim().isEmpty()) {
            sb.append(" - ").append(description);
        }
        
        return sb.toString();
    }

    /**
     * 设置启用状态
     */
    public SysProviderPO setEnabledStatus(boolean enabled) {
        this.enabled = enabled ? 1 : 0;
        return this;
    }

    /**
     * 检查是否为高优先级服务商
     */
    public boolean isHighPriority() {
        return priority != null && priority >= 80;
    }

    /**
     * 获取优先级等级描述
     */
    public String getPriorityLevel() {
        if (priority == null) {
            return "未设置";
        }
        
        if (priority >= 90) {
            return "最高";
        } else if (priority >= 80) {
            return "高";
        } else if (priority >= 60) {
            return "中";
        } else if (priority >= 40) {
            return "低";
        } else {
            return "最低";
        }
    }

    /**
     * 比较优先级
     */
    public int compareTo(SysProviderPO other) {
        if (other == null) {
            return 1;
        }
        
        Integer thisPriority = this.priority != null ? this.priority : 0;
        Integer otherPriority = other.priority != null ? other.priority : 0;
        
        // 降序排列，优先级高的在前
        int priorityCompare = otherPriority.compareTo(thisPriority);
        if (priorityCompare != 0) {
            return priorityCompare;
        }
        
        // 优先级相同时，按名称排序
        if (this.name != null && other.name != null) {
            return this.name.compareTo(other.name);
        }
        
        return 0;
    }

    /**
     * 检查服务商代码是否有效
     */
    public boolean isValidCode() {
        return code != null && 
               code.trim().length() > 0 && 
               code.matches("^[A-Z][A-Z0-9_]*$"); // 大写字母开头，可包含数字和下划线
    }

    @Override
    public String toString() {
        return "SysProviderPO{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                '}';
    }
}
