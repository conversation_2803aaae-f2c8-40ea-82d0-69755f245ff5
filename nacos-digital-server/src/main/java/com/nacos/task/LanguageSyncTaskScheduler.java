package com.nacos.task;

import com.nacos.result.Result;
import com.nacos.service.LanguageSupportSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 语种同步定时任务调度器
 * 负责定时自动同步语种支持数据
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "language-sync.schedule", name = "enabled", havingValue = "true", matchIfMissing = false)
public class LanguageSyncTaskScheduler {

    private final LanguageSupportSyncService languageSupportSyncService;
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 定时同步语种支持数据
     * 默认每日凌晨2点执行
     * 可通过配置 language-sync.schedule.cron 自定义执行时间
     */
    @Scheduled(cron = "${language-sync.schedule.cron:0 0 2 * * ?}")
    public void scheduledSync() {
        String methodName = "scheduledSync";
        LocalDateTime startTime = LocalDateTime.now();
        
        log.info("[{}] 开始执行定时语种同步任务，执行时间: {}", methodName, startTime.format(FORMATTER));
        
        try {
            // 执行LINGYANG语种同步
            Result<String> lingyangResult = syncLingyangWithRetry();
            
            // TODO: 后续可以添加其他服务商的定时同步
            // Result<String> azureResult = syncAzureWithRetry();
            
            LocalDateTime endTime = LocalDateTime.now();
            long durationSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
            
            log.info("[{}] 定时语种同步任务完成，耗时: {}秒，结果: {}", 
                    methodName, durationSeconds, lingyangResult.getMessage());
                    
        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            long durationSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
            
            log.error("[{}] 定时语种同步任务异常，耗时: {}秒", methodName, durationSeconds, e);
            
            // 记录异常但不中断定时任务
            // 确保单次失败不会影响后续的定时执行
        }
    }

    /**
     * 带重试机制的LINGYANG语种同步
     * 
     * @return 同步结果
     */
    private Result<String> syncLingyangWithRetry() {
        String methodName = "syncLingyangWithRetry";
        int maxRetries = 3;
        int retryDelay = 5000; // 5秒
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("[{}] 执行LINGYANG语种同步，第{}次尝试", methodName, attempt);
                
                Result<String> result = languageSupportSyncService.syncLingyangLanguageSupport();
                
                if (result.isSuccess()) {
                    log.info("[{}] LINGYANG语种同步成功，第{}次尝试", methodName, attempt);
                    return result;
                } else {
                    log.warn("[{}] LINGYANG语种同步失败，第{}次尝试，错误: {}", 
                            methodName, attempt, result.getMessage());
                    
                    if (attempt < maxRetries) {
                        log.info("[{}] 等待{}毫秒后重试", methodName, retryDelay);
                        Thread.sleep(retryDelay);
                    }
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("[{}] 重试过程被中断", methodName, e);
                return Result.ERROR("同步过程被中断");
                
            } catch (Exception e) {
                log.error("[{}] LINGYANG语种同步异常，第{}次尝试", methodName, attempt, e);
                
                if (attempt < maxRetries) {
                    try {
                        log.info("[{}] 等待{}毫秒后重试", methodName, retryDelay);
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("[{}] 重试等待被中断", methodName, ie);
                        return Result.ERROR("同步过程被中断");
                    }
                }
            }
        }
        
        String errorMessage = String.format("LINGYANG语种同步失败，已重试%d次", maxRetries);
        log.error("[{}] {}", methodName, errorMessage);
        return Result.ERROR(errorMessage);
    }

    /**
     * 手动触发语种同步（用于测试和紧急同步）
     * 该方法可以被管理接口调用，不受定时任务配置影响
     */
    public Result<String> manualSync() {
        String methodName = "manualSync";
        log.info("[{}] 手动触发语种同步任务", methodName);
        
        try {
            Result<String> result = languageSupportSyncService.syncAllLanguageSupport();
            log.info("[{}] 手动语种同步完成: {}", methodName, result.getMessage());
            return result;
            
        } catch (Exception e) {
            log.error("[{}] 手动语种同步异常", methodName, e);
            return Result.ERROR("手动同步失败: " + e.getMessage());
        }
    }

    /**
     * 获取下次执行时间（用于监控和状态查询）
     * 
     * @return 下次执行时间描述
     */
    public String getNextExecutionInfo() {
        // 根据cron表达式 "0 0 2 * * ?" 计算下次执行时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextExecution;
        
        if (now.getHour() < 2) {
            // 今天还没到2点，下次执行是今天2点
            nextExecution = now.withHour(2).withMinute(0).withSecond(0).withNano(0);
        } else {
            // 今天已经过了2点，下次执行是明天2点
            nextExecution = now.plusDays(1).withHour(2).withMinute(0).withSecond(0).withNano(0);
        }
        
        return String.format("下次执行时间: %s", nextExecution.format(FORMATTER));
    }

    /**
     * 检查定时任务是否启用
     * 
     * @return 是否启用
     */
    public boolean isScheduleEnabled() {
        // 由于使用了@ConditionalOnProperty，如果这个类被实例化，说明定时任务是启用的
        return true;
    }

    /**
     * 获取定时任务状态信息
     * 
     * @return 状态信息
     */
    public String getSchedulerStatus() {
        return String.format("语种同步定时任务状态: 已启用, %s", getNextExecutionInfo());
    }
}
