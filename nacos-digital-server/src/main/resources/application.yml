spring:
  profiles:
    active: @env@
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${file.uploadPath}
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB

# 腾讯云API配置
tencent:
  cloud:
    api:
      secret-id: ${TENCENT_SECRET_ID:AKIDRnw8GBtOhojQvDH8pW2MalmbFixzpdOB}
      secret-key: ${TENCENT_SECRET_KEY:qW8xpXNG5mGWt7B4nLgppfJn5DAwXu54}

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.nacos.entity
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: is_deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
task:
  course:
    enabled: true  # 任务开关
#    cron: "0 0-59 0-6 * * ?"  # 每天0-6点之间随机时间执行
    cron: "0 1-5 * * * ?"  # 每1-5分钟之间随机时间执行
    min-increment: 1  # 最小增加数
    max-increment: 10 # 最大增加数

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    display-request-duration: true
    groups-order: ASC
    operations-sorter: alpha
    tags-sorter: alpha
  packages-to-scan: com.nacos.controller
  paths-to-match: /**
  default-flat-param-object: true  # 扁平化参数对象
  group-configs:
    - group: admin
      display-name: "Admin API (数据管理)"
      paths-to-match: /admin/v1/**
    - group: avatar
      display-name: "Avatar API (数字人业务)"
      paths-to-match: /api/v1/avatars/**
    - group: media
      display-name: "Media API (媒体处理)"
      paths-to-match: /api/v1/media/**
    - group: task
      display-name: "Task API (任务管理)"
      paths-to-match: /api/v1/tasks/**
    - group: system
      display-name: "System API (系统管理)"
      paths-to-match: /api/v1/system/**
    - group: legacy
      display-name: "Legacy API (兼容层)"
      paths-to-match: /avatar/**, /opear/**, /audio/**, /video/**, /upload/**

# 数字人处理配置
digital:
  # OSS配置（可选，默认使用原有硬编码配置）
  oss:
    # 是否启用自定义OSS配置，false=使用原配置，true=使用下面的自定义配置
    enabled: false
    # 以下配置仅在enabled=true时生效
    endpoint: https://oss-cn-beijing.aliyuncs.com
    access-key-id: LTAI5t9A9sRyNs1a2AsDT7Hp
    secret-access-key: ******************************
    bucket-name: idotdesign
    access-path: https://cdn.diandiansheji.com/
    connection-timeout: 30000
    socket-timeout: 60000
    max-error-retry: 5
    max-connections: 200
    idle-connection-time: 300
    connection-ttl: 600000

  audio:
    # 异步音频生成配置
    # 音频任务扫描频率（毫秒）
    task-scan-rate: 10000 # 10秒
    # 音频处理线程池大小
    thread-pool-size: 5
    # 每次处理的任务批量大小
    batch-size: 10
    # 任务超时时间（分钟）
    timeout-minutes: 5

    # 同步音频生成配置
    sync:
      # 是否启用同步音频生成功能
      enabled: true
      # 默认音色ID
      default-voice-id: presenter_female
      # HTTP接口超时时间（秒）
      timeout: 120
      # API调用超时时间（秒）
      api-timeout: 60
      # 是否保存任务记录
      save-task-record: true
      # 全局限流速率（每秒请求数）
      global-rate-limit: 10.0
      # 用户限流速率（每秒请求数）
      user-rate-limit: 5.0
      # 最大并发请求数
      max-concurrent: 10
      # 任务记录保留天数
      task-record-retention-days: 30
      # 是否启用限流功能
      enable-rate-limit: true
      # 是否启用并发控制
      enable-concurrency-control: true
      # 音频文件最大大小（MB）
      max-audio-size-mb: 50
      # 文本最大长度（字符数）
      max-text-length: 10000
      # 重试次数
      retry-count: 3
      # 重试间隔（毫秒）
      retry-interval-ms: 1000
      # 是否启用异步回调
      enable-async-callback: false
      # 回调超时时间（秒）
      callback-timeout: 30
  video:
    # 视频任务扫描频率（毫秒）
    task-scan-rate: 10000 # 10秒
    task:
      concurrent-limit: 1  # 单用户并发任务数限制
      queue-size: 100      # 队列大小限制
      timeout-minutes: 30  # 任务超时时间(分钟)
      retry:
        max-attempts: 3    # 最大重试次数
        initial-interval: 1000  # 初始重试间隔(毫秒)
        multiplier: 2.0    # 重试间隔倍数
        max-interval: 10000  # 最大重试间隔(毫秒)

    # 视频翻译服务商配置
    translate:
      # 默认服务商
      default-provider: LINGYANG
      # 是否启用故障转移
      fallback-enabled: true
      # 全局超时时间（分钟）
      global-timeout-minutes: 30
      # 轮询间隔（毫秒）
      poll-interval-ms: 5000
      # 最大重试次数
      max-retries: 60

      # 服务商配置
      providers:
        # 羚羊平台配置
        lingyang:
          enabled: true
          priority: 1
          timeout-minutes: 30
          description: "羚羊平台视频翻译服务"
          max-concurrent: 5
          retry-count: 3
          health-check-enabled: true

        # 阿里云配置（备用）
        aliyun:
          enabled: false
          priority: 2
          timeout-minutes: 25
          description: "阿里云视频翻译服务"
          max-concurrent: 3
          retry-count: 2
          health-check-enabled: true

  # 语种支持数据同步配置
  language-sync:
    # 语种同步总开关
    enabled: true

    # 定时同步任务配置
    schedule:
      # 定时任务开关
      enabled: true
      # 定时执行表达式（默认每日凌晨2点执行）
      cron: "0 0 2 * * ?"
      # 任务超时时间（分钟）
      timeout-minutes: 10
      # 失败重试次数
      retry-count: 3
      # 重试间隔（秒）
      retry-interval-seconds: 5

    # 服务商配置
    providers:
      # 羚羊平台语种同步配置
      lingyang:
        # 是否启用LINGYANG语种同步
        enabled: true
        # API调用超时时间（秒）
        timeout-seconds: 30
        # 最大重试次数
        retry-times: 3
        # 重试间隔（毫秒）
        retry-interval-ms: 5000
        # 批量处理大小
        batch-size: 1000
        # 描述信息
        description: "羚羊平台语种支持数据同步"

      # Azure语种同步配置（预留）
      azure:
        # 是否启用Azure语种同步
        enabled: false
        # API调用超时时间（秒）
        timeout-seconds: 30
        # 最大重试次数
        retry-times: 3
        # 重试间隔（毫秒）
        retry-interval-ms: 5000
        # 批量处理大小
        batch-size: 1000
        # 描述信息
        description: "Azure语种支持数据同步"

    # 数据管理配置
    data:
      # 数据一致性检查开关
      consistency-check-enabled: true
      # 过期数据清理开关
      cleanup-enabled: true
      # 数据保留天数
      retention-days: 90
      # 缓存刷新间隔（分钟）
      cache-refresh-interval-minutes: 60

    # 监控配置
    monitoring:
      # 是否启用同步监控
      enabled: true
      # 同步状态统计开关
      statistics-enabled: true
      # 同步日志保留天数
      log-retention-days: 30

# JWT配置
jwt:
  secret: ${JWT_SECRET:diandiansheji_digital_jwt_secret_key}  # JWT密钥，与其他服务保持一致
  expiration: 86400000  # 24小时过期时间

# Azure语音服务配置
azure:
  speech:
    subscription-key: ${AZURE_SPEECH_KEY:ec363118097946b1adae818d9169b3ee}  # Azure语音服务订阅密钥
    region: ${AZURE_SPEECH_REGION:eastus}   # 服务区域
    output-dir: ${AZURE_SPEECH_OUTPUT_DIR:./speech-output}  # 输出目录

# 禅境API配置
changjin_secret_key: ${CHANGJIN_SECRET_KEY:ee0cf34fb2c74abd882b0f26052bd094}

# 音频生成限流配置
audio:
  generation:
    rate-limit:
      # 全局限流配置 - 每秒允许的请求数
      global: 2.0
      # 用户级限流配置 - 每秒允许的请求数
      user: 0.5
      # 用户限流器缓存过期时间（分钟）
      user-cache-expire: 30

# 音色配置
voice:
  migration:
    # 双写机制开关
    dual-write-enabled: true

    # 新表读取开关
    new-table-read-enabled: false

    # 双写失败处理策略
    # IGNORE: 忽略失败，继续执行
    # ROLLBACK: 回滚事务
    # LOG_ONLY: 只记录日志
    failure-strategy: LOG_ONLY

    # 数据同步验证开关
    sync-validation-enabled: false

    # 验证采样率 (0.0-1.0)
    validation-sample-rate: 0.1

    # 双写超时时间（毫秒）
    dual-write-timeout-ms: 5000

    # 异步双写开关
    async-dual-write-enabled: false

# ==================== 统一任务系统配置 ====================
# 基于策略模式+工厂模式+Redis消息队列的统一任务处理架构
# 支持视频翻译、音频生成、视频编辑三种任务类型的统一调度和处理
universal-task:
  # 任务系统总开关
  enabled: true

  # 视频翻译任务专用配置
  video-translate:
    enabled: false                               # 视频翻译统一任务系统开关，默认false确保向后兼容
    fallback-enabled: true                       # 兜底机制开关，当统一系统失败时使用原系统

  # Redis消息队列配置
  redis:
    # 队列名称配置
    queues:
      video-translate: "TOPIC_VIDEO_TRANSLATE"    # 视频翻译队列
      audio-generate: "TOPIC_AUDIO_GENERATE"      # 音频生成队列
      video-edit: "TOPIC_VIDEO_EDIT"              # 视频编辑队列

    # 消息序列化配置
    serialization:
      type: "JSON"                                # 序列化类型：JSON/PROTOBUF
      charset: "UTF-8"                           # 字符编码

    # 消息重试策略
    retry:
      max-attempts: 3                            # 最大重试次数
      initial-interval: 1000                     # 初始重试间隔(毫秒)
      multiplier: 2.0                           # 重试间隔倍数
      max-interval: 10000                       # 最大重试间隔(毫秒)

    # 死信队列配置
    dead-letter:
      enabled: true                             # 是否启用死信队列
      queue-suffix: "_DLQ"                      # 死信队列后缀
      ttl: 86400000                            # 死信消息TTL(毫秒，24小时)

  # 任务处理器配置
  processors:
    # 视频翻译处理器专用配置
    video-translate:
      enabled: true                            # 视频翻译处理器开关
      timeout-minutes: 30                      # 处理超时时间(分钟)
      max-concurrent: 5                        # 最大并发处理数
      retry-attempts: 3                        # 重试次数

    # 处理器健康检查
    health-check:
      enabled: true                             # 是否启用健康检查
      interval: 30000                          # 检查间隔(毫秒)
      timeout: 5000                            # 检查超时(毫秒)

    # 处理器注册配置
    registration:
      auto-scan: true                          # 自动扫描处理器
      base-packages: "com.nacos.service.processor" # 扫描包路径

  # 兜底定时任务配置
  fallback-scheduler:
    enabled: true                              # 是否启用兜底调度器
    fixed-delay: 60000                        # 执行间隔(毫秒，1分钟)
    initial-delay: 30000                      # 初始延迟(毫秒)
    batch-size: 50                           # 每次处理的任务数量
    max-retry-hours: 24                      # 最大重试时间(小时)

  # 任务状态管理
  status:
    # 状态同步配置
    sync:
      enabled: true                           # 是否启用状态同步
      batch-size: 100                        # 批量同步大小

    # 状态映射配置
    mapping:
      strict-mode: false                      # 严格模式：true=严格映射，false=兼容模式

  # 监控和日志配置
  monitoring:
    # 性能监控
    metrics:
      enabled: true                           # 是否启用性能监控
      export-interval: 60000                  # 指标导出间隔(毫秒)

    # 任务执行日志
    logging:
      enabled: true                           # 是否启用任务日志
      level: "INFO"                          # 日志级别
      include-context: true                   # 是否包含上下文信息

  # 任务超时配置
  timeout:
    default: 1800000                         # 默认超时时间(毫秒，30分钟)
    video-translate: 1800000                 # 视频翻译超时(毫秒，30分钟)
    audio-generate: 300000                   # 音频生成超时(毫秒，5分钟)
    video-edit: 3600000                      # 视频编辑超时(毫秒，60分钟)

