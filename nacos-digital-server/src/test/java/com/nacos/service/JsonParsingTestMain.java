package com.nacos.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.entity.dto.VideoTranslateRequestDTO;

/**
 * JSON解析测试主类
 * 用于验证VideoTranslateRequestDTO的JSON序列化和反序列化
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
public class JsonParsingTestMain {

    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper();
        
        System.out.println("=== 开始JSON解析测试 ===");
        
        // 测试1：完整JSON解析
        testCompleteJsonParsing(objectMapper);
        
        // 测试2：不完整JSON解析
        testIncompleteJsonParsing(objectMapper);
        
        // 测试3：JSON序列化
        testJsonSerialization(objectMapper);
        
        System.out.println("=== JSON解析测试完成 ===");
    }
    
    private static void testCompleteJsonParsing(ObjectMapper objectMapper) {
        System.out.println("\n--- 测试完整JSON解析 ---");
        
        // 模拟修改后的完整JSON（包含所有必需字段）
        String completeJson = """
            {
                "userId": "user123",
                "videoUrl": "https://example.com/video.mp4",
                "taskName": "视频翻译任务 - cn → en",
                "sourceLanguage": "cn",
                "targetLanguage": "en",
                "voiceId": "Attractive_Girl",
                "useOriginalVoice": false,
                "extParams": null
            }
            """;

        try {
            VideoTranslateRequestDTO dto = objectMapper.readValue(completeJson, VideoTranslateRequestDTO.class);
            
            System.out.println("✅ 完整JSON解析成功:");
            System.out.println("  用户ID: " + dto.getUserId());
            System.out.println("  视频URL: " + dto.getVideoUrl());
            System.out.println("  任务名称: " + dto.getTaskName());
            System.out.println("  源语言: " + dto.getSourceLanguage());
            System.out.println("  目标语言: " + dto.getTargetLanguage());
            System.out.println("  音色ID: " + dto.getVoiceId());
            System.out.println("  使用原声: " + dto.getUseOriginalVoice());
            
            // 验证业务方法
            System.out.println("  参数验证(含videoUrl): " + dto.isValidWithVideoUrl());
            System.out.println("  语言对描述: " + dto.getLanguagePairDescription());
            
        } catch (Exception e) {
            System.out.println("❌ 完整JSON解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testIncompleteJsonParsing(ObjectMapper objectMapper) {
        System.out.println("\n--- 测试不完整JSON解析 ---");
        
        // 原始数据库中的不完整JSON
        String incompleteJson = """
            {
                "useOriginalVoice": false,
                "voiceId": "Attractive_Girl",
                "targetLanguage": "en",
                "sourceLanguage": "cn"
            }
            """;

        try {
            VideoTranslateRequestDTO dto = objectMapper.readValue(incompleteJson, VideoTranslateRequestDTO.class);
            
            System.out.println("⚠️ 不完整JSON解析成功但字段缺失:");
            System.out.println("  用户ID: " + dto.getUserId() + " (缺失)");
            System.out.println("  视频URL: " + dto.getVideoUrl() + " (缺失)");
            System.out.println("  任务名称: " + dto.getTaskName() + " (缺失)");
            System.out.println("  源语言: " + dto.getSourceLanguage());
            System.out.println("  目标语言: " + dto.getTargetLanguage());
            System.out.println("  音色ID: " + dto.getVoiceId());
            System.out.println("  使用原声: " + dto.getUseOriginalVoice());
            
            // 验证业务方法
            System.out.println("  基础参数验证: " + dto.isValid());
            System.out.println("  完整参数验证: " + dto.isValidWithVideoUrl());
            
        } catch (Exception e) {
            System.out.println("❌ 不完整JSON解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testJsonSerialization(ObjectMapper objectMapper) {
        System.out.println("\n--- 测试JSON序列化 ---");
        
        // 创建完整的DTO对象
        VideoTranslateRequestDTO dto = new VideoTranslateRequestDTO();
        dto.setUserId("user123");
        dto.setVideoUrl("https://example.com/video.mp4");
        dto.setTaskName("视频翻译任务 - cn → en");
        dto.setSourceLanguage("cn");
        dto.setTargetLanguage("en");
        dto.setVoiceId("Attractive_Girl");
        dto.setUseOriginalVoice(false);
        dto.setExtParams(null);

        try {
            String json = objectMapper.writeValueAsString(dto);
            System.out.println("✅ JSON序列化成功:");
            System.out.println(json);
            
            // 测试反序列化
            VideoTranslateRequestDTO deserializedDto = objectMapper.readValue(json, VideoTranslateRequestDTO.class);
            System.out.println("✅ 反序列化验证:");
            System.out.println("  用户ID一致: " + dto.getUserId().equals(deserializedDto.getUserId()));
            System.out.println("  视频URL一致: " + dto.getVideoUrl().equals(deserializedDto.getVideoUrl()));
            
        } catch (Exception e) {
            System.out.println("❌ JSON序列化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
