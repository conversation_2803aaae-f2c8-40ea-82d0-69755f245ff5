package com.nacos.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.entity.dto.VideoTranslateRequestDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 视频翻译JSON解析测试
 * 用于验证VideoTranslateRequestDTO的JSON序列化和反序列化
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class VideoTranslateJsonParsingTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @DisplayName("测试完整JSON解析")
    void testCompleteJsonParsing() {
        // 模拟修改后的完整JSON（包含所有必需字段）
        String completeJson = """
            {
                "userId": "user123",
                "videoUrl": "https://example.com/video.mp4",
                "taskName": "视频翻译任务 - cn → en",
                "sourceLanguage": "cn",
                "targetLanguage": "en",
                "voiceId": "Attractive_Girl",
                "useOriginalVoice": false,
                "extParams": null
            }
            """;

        try {
            VideoTranslateRequestDTO dto = objectMapper.readValue(completeJson, VideoTranslateRequestDTO.class);
            
            // 验证解析结果
            assertNotNull(dto, "DTO不应为null");
            assertEquals("user123", dto.getUserId(), "用户ID应正确解析");
            assertEquals("https://example.com/video.mp4", dto.getVideoUrl(), "视频URL应正确解析");
            assertEquals("视频翻译任务 - cn → en", dto.getTaskName(), "任务名称应正确解析");
            assertEquals("cn", dto.getSourceLanguage(), "源语言应正确解析");
            assertEquals("en", dto.getTargetLanguage(), "目标语言应正确解析");
            assertEquals("Attractive_Girl", dto.getVoiceId(), "音色ID应正确解析");
            assertEquals(false, dto.getUseOriginalVoice(), "使用原声标志应正确解析");
            
            // 验证业务方法
            assertTrue(dto.isValidWithVideoUrl(), "完整参数应通过验证");
            assertFalse(dto.isSameLanguage(), "源语言和目标语言不同");
            assertEquals("cn → en", dto.getLanguagePairDescription(), "语言对描述应正确");
            
            System.out.println("✅ 完整JSON解析成功: " + dto);
            
        } catch (Exception e) {
            fail("完整JSON解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    @DisplayName("测试原始不完整JSON解析")
    void testIncompleteJsonParsing() {
        // 原始数据库中的不完整JSON
        String incompleteJson = """
            {
                "useOriginalVoice": false,
                "voiceId": "Attractive_Girl",
                "targetLanguage": "en",
                "sourceLanguage": "cn"
            }
            """;

        try {
            VideoTranslateRequestDTO dto = objectMapper.readValue(incompleteJson, VideoTranslateRequestDTO.class);
            
            // 验证解析结果
            assertNotNull(dto, "DTO不应为null");
            assertNull(dto.getUserId(), "用户ID应为null（缺失字段）");
            assertNull(dto.getVideoUrl(), "视频URL应为null（缺失字段）");
            assertNull(dto.getTaskName(), "任务名称应为null（缺失字段）");
            assertEquals("cn", dto.getSourceLanguage(), "源语言应正确解析");
            assertEquals("en", dto.getTargetLanguage(), "目标语言应正确解析");
            assertEquals("Attractive_Girl", dto.getVoiceId(), "音色ID应正确解析");
            assertEquals(false, dto.getUseOriginalVoice(), "使用原声标志应正确解析");
            
            // 验证业务方法
            assertFalse(dto.isValidWithVideoUrl(), "不完整参数应无法通过验证");
            assertTrue(dto.isValid(), "基础参数验证应通过（不包含videoUrl）");
            
            System.out.println("⚠️ 不完整JSON解析成功但验证失败: " + dto);
            
        } catch (Exception e) {
            fail("不完整JSON解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    @DisplayName("测试JSON序列化")
    void testJsonSerialization() {
        // 创建完整的DTO对象
        VideoTranslateRequestDTO dto = new VideoTranslateRequestDTO();
        dto.setUserId("user123");
        dto.setVideoUrl("https://example.com/video.mp4");
        dto.setTaskName("视频翻译任务 - cn → en");
        dto.setSourceLanguage("cn");
        dto.setTargetLanguage("en");
        dto.setVoiceId("Attractive_Girl");
        dto.setUseOriginalVoice(false);
        dto.setExtParams(null);

        try {
            String json = objectMapper.writeValueAsString(dto);
            assertNotNull(json, "序列化结果不应为null");
            assertTrue(json.contains("\"userId\":\"user123\""), "JSON应包含用户ID");
            assertTrue(json.contains("\"videoUrl\":\"https://example.com/video.mp4\""), "JSON应包含视频URL");
            assertTrue(json.contains("\"sourceLanguage\":\"cn\""), "JSON应包含源语言");
            assertTrue(json.contains("\"targetLanguage\":\"en\""), "JSON应包含目标语言");
            
            System.out.println("✅ JSON序列化成功: " + json);
            
            // 测试反序列化
            VideoTranslateRequestDTO deserializedDto = objectMapper.readValue(json, VideoTranslateRequestDTO.class);
            assertEquals(dto.getUserId(), deserializedDto.getUserId(), "反序列化后用户ID应一致");
            assertEquals(dto.getVideoUrl(), deserializedDto.getVideoUrl(), "反序列化后视频URL应一致");
            
        } catch (Exception e) {
            fail("JSON序列化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    @DisplayName("测试字段缺失情况下的验证")
    void testValidationWithMissingFields() {
        VideoTranslateRequestDTO dto = new VideoTranslateRequestDTO();
        
        // 只设置部分字段
        dto.setSourceLanguage("cn");
        dto.setTargetLanguage("en");
        dto.setVoiceId("Attractive_Girl");
        dto.setUseOriginalVoice(false);
        
        // userId 和 videoUrl 缺失
        assertFalse(dto.isValid(), "缺少userId时基础验证应失败");
        assertFalse(dto.isValidWithVideoUrl(), "缺少videoUrl时完整验证应失败");
        
        // 补充userId
        dto.setUserId("user123");
        assertTrue(dto.isValid(), "补充userId后基础验证应通过");
        assertFalse(dto.isValidWithVideoUrl(), "仍缺少videoUrl时完整验证应失败");
        
        // 补充videoUrl
        dto.setVideoUrl("https://example.com/video.mp4");
        assertTrue(dto.isValid(), "补充所有字段后基础验证应通过");
        assertTrue(dto.isValidWithVideoUrl(), "补充所有字段后完整验证应通过");
        
        System.out.println("✅ 字段验证测试通过");
    }
}
